{"name": "binance-tick-collector", "version": "1.0.0", "description": "Binance Futures tick data collector with Redis queue and SQLite storage", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest"}, "keywords": ["binance", "futures", "websocket", "redis", "sqlite", "tick-data"], "author": "AI Agent", "license": "MIT", "dependencies": {"better-sqlite3": "^9.4.3", "bullmq": "^5.1.1", "express": "^4.19.2", "ioredis": "^5.3.2", "ws": "^8.17.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.0"}}