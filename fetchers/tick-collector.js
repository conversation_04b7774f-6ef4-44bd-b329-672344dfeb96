const WebSocketMonitor = require('./websocket-monitor');
const { tickQueue } = require('./queue');
const logger = require('./logger');
const symbolsConfig = require('./symbols.json');
const config = require('./config.json');
const dotenv = require('dotenv');
// Load environment variables from .env file
dotenv.config();
const debugMode = process.env.DEBUG_MODE === 'true';

class TickCollector {
  constructor() {
    this.monitors = new Map();
    this.symbols = new Map();
    this.pairStats = new Map();
    this.healthServer = null;
    this.initializeSymbols();
  }

  initializeSymbols() {
    // Flatten all symbols from all connections
    const allPairs = [];
    symbolsConfig.connections.forEach(connection => {
      connection.pairs.forEach(pair => {
        allPairs.push({
          connectionId: connection.id,
          pair: pair
        });
      });
    });
    
    // Distribute pairs across connections based on pairsPerConnection limit
    const pairsPerConnection = config.websocket.pairsPerConnection;
    const totalConnectionsNeeded = Math.ceil(allPairs.length / pairsPerConnection);
    
    // Create distributed connections
    this.distributedConnections = [];
    for (let i = 0; i < totalConnectionsNeeded; i++) {
      const startIndex = i * pairsPerConnection;
      const endIndex = Math.min(startIndex + pairsPerConnection, allPairs.length);
      const connectionPairs = allPairs.slice(startIndex, endIndex);
      
      this.distributedConnections.push({
        id: i + 1,
        pairs: connectionPairs.map(item => item.pair)
      });
      
      // Initialize symbols map
      connectionPairs.forEach(item => {
        this.symbols.set(item.pair, {
          connectionId: i + 1,
          pair: item.pair
        });
      });
    }
    
    logger.info(`Initialized ${this.symbols.size} symbols across ${this.distributedConnections.length} connections (limited to ${pairsPerConnection} pairs per connection)`);
  }

  start() {
    logger.info('Starting kline collector...');
    
    // Get the number of pairs to use per connection from config
    const pairsPerConnection = config.websocket.pairsPerConnection;
    
    // Create WebSocket monitors for each distributed connection
    this.distributedConnections.forEach(connection => {
      const wsUrl = `wss://fstream.binance.com/ws/${connection.pairs.map(p => p.toLowerCase() + '@kline_1m').join('/')}`;
      const monitor = new WebSocketMonitor(connection.id, wsUrl);
      
      monitor.setHandlers({
        onOpen: () => {
          logger.info(`Connected to Binance Futures WebSocket for connection ${connection.id} with ${connection.pairs.length} pairs`);
        },
        onMessage: (data) => {
          this.processKlineData(data, connection.id);
        },
        onError: (error) => {
          logger.error(`WebSocket error for connection ${connection.id}: ${error.message}`);
        },
        onClose: (code, reason) => {
          logger.warn(`WebSocket closed for connection ${connection.id}. Code: ${code}, Reason: ${reason}`);
        }
      });
      
      this.monitors.set(connection.id, monitor);
      monitor.connect();
    });
  }

  processKlineData(data, connectionId) {
    try {
      const klineData = JSON.parse(data);
      
      // Validate kline data
      if (!klineData || !klineData.e || klineData.e !== 'kline' || !klineData.k) {
        debugMode && logger.debug('Received invalid kline data');
        return;
      }
      
      // Transform kline data to our format with proper validation
      const kline = klineData.k;
      const symbol = (klineData.s || '').toLowerCase();

      // Update pair statistics
      this.updatePairStats(symbol);

      const processedKline = {
        eventType: 'kline',
        eventTime: parseInt(klineData.E) || 0,
        symbol: symbol,
        kline: {
          startTime: parseInt(kline.t) || 0,
          endTime: parseInt(kline.T) || 0,
          symbol: symbol,
          interval: (kline.i || ''),
          firstTradeId: parseInt(kline.f) || 0,
          lastTradeId: parseInt(kline.L) || 0,
          openPrice: parseFloat(kline.o) || 0,
          closePrice: parseFloat(kline.c) || 0,
          highPrice: parseFloat(kline.h) || 0,
          lowPrice: parseFloat(kline.l) || 0,
          baseAssetVolume: parseFloat(kline.v) || 0,
          numberOfTrades: parseInt(kline.n) || 0,
          isClosed: Boolean(kline.x),
          quoteAssetVolume: parseFloat(kline.q) || 0,
          takerBuyBaseAssetVolume: parseFloat(kline.V) || 0,
          takerBuyQuoteAssetVolume: parseFloat(kline.Q) || 0
        }
      };

      // Add to Redis queue
      this.enqueueKline(processedKline);
    } catch (error) {
      logger.error(`Error processing kline data: ${error.message}`);
      this.updatePairStats(null, true); // Update error count
    }
  }

  updatePairStats(symbol, isError = false) {
    if (isError) {
      // Update error count for all pairs or specific symbol
      if (symbol) {
        const stats = this.pairStats.get(symbol) || { recordCount: 0, errors: 0, lastUpdate: null, status: 'unknown' };
        stats.errors++;
        stats.status = 'error';
        this.pairStats.set(symbol, stats);

        // Notify health server if available
        if (this.healthServer) {
          this.healthServer.updatePairState(symbol, stats);
        }
      }
      return;
    }

    if (!symbol) return;

    const stats = this.pairStats.get(symbol) || { recordCount: 0, errors: 0, lastUpdate: null, status: 'unknown' };
    stats.recordCount++;
    stats.lastUpdate = Date.now();
    stats.status = 'active';

    this.pairStats.set(symbol, stats);

    // Notify health server if available
    if (this.healthServer) {
      this.healthServer.updatePairState(symbol, stats);
    }
  }

  setHealthServer(healthServer) {
    this.healthServer = healthServer;
  }

  async enqueueKline(kline) {
    try {
      await tickQueue.add('kline', kline, {
        jobId: `${kline.symbol}-${kline.kline.startTime}-${Date.now()}`,
        removeOnComplete: true
      });
      
      debugMode && logger.debug(`Enqueued kline for ${kline.symbol} at ${kline.kline.startTime}`);
    } catch (error) {
      logger.error(`Failed to enqueue kline for ${kline.symbol}: ${error.message}`);
    }
  }

  getStatus() {
    const status = {};
    for (const [id, monitor] of this.monitors) {
      status[id] = monitor.getStatus();
    }
    return status;
  }

  close() {
    logger.info('Closing all WebSocket connections...');
    for (const [id, monitor] of this.monitors) {
      monitor.close();
    }
  }
}

module.exports = TickCollector;
