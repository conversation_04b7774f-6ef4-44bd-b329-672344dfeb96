[2025-07-24T21:02:09.878Z] [INFO] Starting Binance Tick Collector System...
[2025-07-24T21:02:09.879Z] [INFO] Initialized 115 symbols across 2 connections
[2025-07-24T21:02:09.879Z] [INFO] Starting kline collector...
[2025-07-24T21:02:09.911Z] [INFO] SQLite database initialized: /Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/data/ticks-2025-07-25.sqlite
[2025-07-24T21:02:09.911Z] [INFO] Starting 10 tick workers...
[2025-07-24T21:02:09.913Z] [INFO] Started 10 tick workers
[2025-07-24T21:02:09.915Z] [INFO] Binance Tick Collector System started successfully
[2025-07-24T21:02:09.915Z] [INFO] === System Information ===
[2025-07-24T21:02:09.915Z] [INFO] CPU Cores: 10
[2025-07-24T21:02:09.916Z] [INFO] Total Memory: 32 GB
[2025-07-24T21:02:09.916Z] [INFO] Platform: darwin arm64
[2025-07-24T21:02:09.916Z] [INFO] Node.js Version: v19.9.0
[2025-07-24T21:02:09.916Z] [INFO] ========================
[2025-07-24T21:02:09.917Z] [INFO] Health server listening on port 3000
[2025-07-24T21:02:09.986Z] [INFO] Redis connection ready
[2025-07-24T21:02:10.885Z] [INFO] WebSocket connection 2 opened: wss://fstream.binance.com/ws/uniusdt@kline_1m/yfiusdt@kline_1m/dashusdt@kline_1m/zecusdt@kline_1m/batusdt@kline_1m/crvusdt@kline_1m/scusdt@kline_1m/neousdt@kline_1m/egldusdt@kline_1m/sfpusdt@kline_1m/anclusdt@kline_1m/oneusdt@kline_1m/lrcusdt@kline_1m/runeusdt@kline_1m/mkrusdt@kline_1m/lunausdt@kline_1m/ksmusdt@kline_1m/xemusdt@kline_1m/iotausdt@kline_1m/xtzusdt@kline_1m/rvnusdt@kline_1m/kavausdt@kline_1m/ctkusdt@kline_1m/arusdt@kline_1m/zenusdt@kline_1m/sxpusdt@kline_1m/sklusdt@kline_1m/injusdt@kline_1m/stgusdt@kline_1m/dydxusdt@kline_1m/gmxusdt@kline_1m/opulusdt@kline_1m/blurusdt@kline_1m/rndrusdt@kline_1m/ldousdt@kline_1m/snmusdt@kline_1m/agixusdt@kline_1m/radusdt@kline_1m/arusdt@kline_1m/beamusdt@kline_1m/imusdt@kline_1m/tusdt@kline_1m/joeusdt@kline_1m/achusdt@kline_1m/gftusdt@kline_1m/cotiusdt@kline_1m/idexusdt@kline_1m/aliceusdt@kline_1m/superusdt@kline_1m/suiusdt@kline_1m/pythusdt@kline_1m/wldusdt@kline_1m/arkmusdt@kline_1m/onetusdt@kline_1m/wusdt@kline_1m/pendleusdt@kline_1m/tiausdt@kline_1m/bonkusdt@kline_1m/ensusdt@kline_1m/altusdt@kline_1m/maverickusdt@kline_1m/ckbusdt@kline_1m/hftusdt@kline_1m/bigtimeusdt@kline_1m/renderusdt@kline_1m/omusdt@kline_1m/jupusdt@kline_1m/wifusdt@kline_1m
[2025-07-24T21:02:10.886Z] [INFO] Connected to Binance Futures WebSocket for connection 2
[2025-07-24T21:02:10.886Z] [INFO] WebSocket connection 1 opened: wss://fstream.binance.com/ws/btcusdt@kline_1m/ethusdt@kline_1m/bnbusdt@kline_1m/xrpusdt@kline_1m/adausdt@kline_1m/solusdt@kline_1m/dogeusdt@kline_1m/dotusdt@kline_1m/maticusdt@kline_1m/ltcusdt@kline_1m/linkusdt@kline_1m/trxusdt@kline_1m/etcusdt@kline_1m/icpusdt@kline_1m/nearusdt@kline_1m/atomusdt@kline_1m/xlmusdt@kline_1m/bchusdt@kline_1m/ftmusdt@kline_1m/apeusdt@kline_1m/sandusdt@kline_1m/eosusdt@kline_1m/aaveusdt@kline_1m/algousdt@kline_1m/thetausdt@kline_1m/filusdt@kline_1m/axsusdt@kline_1m/hbarusdt@kline_1m/vetusdt@kline_1m/icxusdt@kline_1m/chzusdt@kline_1m/manausdt@kline_1m/enjusdt@kline_1m/flowusdt@kline_1m/klayusdt@kline_1m/roseusdt@kline_1m/galausdt@kline_1m/gmtusdt@kline_1m/sushiusdt@kline_1m/1inchusdt@kline_1m/cakeusdt@kline_1m/zilusdt@kline_1m/hotusdt@kline_1m/balusdt@kline_1m/snxusdt@kline_1m/compusdt@kline_1m/celousdt@kline_1m/wavesusdt@kline_1m
[2025-07-24T21:02:10.886Z] [INFO] Connected to Binance Futures WebSocket for connection 1
[2025-07-24T21:04:16.057Z] [INFO] Received SIGINT, shutting down gracefully...
[2025-07-24T21:04:16.059Z] [INFO] Shutting down Binance Tick Collector System...
[2025-07-24T21:04:16.059Z] [INFO] Closing all WebSocket connections...
[2025-07-24T21:04:16.060Z] [INFO] Closing WebSocket connection 1
[2025-07-24T21:04:16.061Z] [INFO] Closing WebSocket connection 2
[2025-07-24T21:04:16.062Z] [INFO] Closing tick workers...
[2025-07-24T21:04:16.063Z] [INFO] Health server closed
[2025-07-24T21:04:16.078Z] [INFO] SQLite database closed: /Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/data/ticks-2025-07-25.sqlite
[2025-07-24T21:04:16.078Z] [INFO] All tick workers closed
[2025-07-24T21:04:16.078Z] [INFO] Binance Tick Collector System shut down successfully
[2025-07-24T21:04:55.958Z] [INFO] Starting Binance Tick Collector System...
[2025-07-24T21:04:55.959Z] [INFO] Initialized 115 symbols across 2 connections
[2025-07-24T21:04:55.960Z] [INFO] Starting kline collector...
[2025-07-24T21:04:55.986Z] [INFO] SQLite database initialized: /Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/data/ticks-2025-07-25.sqlite
[2025-07-24T21:04:55.987Z] [INFO] Starting 10 tick workers...
[2025-07-24T21:04:55.989Z] [INFO] Started 10 tick workers
[2025-07-24T21:04:55.991Z] [INFO] Binance Tick Collector System started successfully
[2025-07-24T21:04:55.991Z] [INFO] === System Information ===
[2025-07-24T21:04:55.991Z] [INFO] CPU Cores: 10
[2025-07-24T21:04:55.991Z] [INFO] Total Memory: 32 GB
[2025-07-24T21:04:55.991Z] [INFO] Platform: darwin arm64
[2025-07-24T21:04:55.991Z] [INFO] Node.js Version: v19.9.0
[2025-07-24T21:04:55.991Z] [INFO] ========================
[2025-07-24T21:04:55.993Z] [INFO] Health server listening on port 3000
[2025-07-24T21:04:56.064Z] [INFO] Redis connection ready
[2025-07-24T21:04:56.962Z] [INFO] WebSocket connection 1 opened: wss://fstream.binance.com/ws/btcusdt@kline_1m/ethusdt@kline_1m/bnbusdt@kline_1m/xrpusdt@kline_1m/adausdt@kline_1m/solusdt@kline_1m/dogeusdt@kline_1m/dotusdt@kline_1m/maticusdt@kline_1m/ltcusdt@kline_1m/linkusdt@kline_1m/trxusdt@kline_1m/etcusdt@kline_1m/icpusdt@kline_1m/nearusdt@kline_1m/atomusdt@kline_1m/xlmusdt@kline_1m/bchusdt@kline_1m/ftmusdt@kline_1m/apeusdt@kline_1m/sandusdt@kline_1m/eosusdt@kline_1m/aaveusdt@kline_1m/algousdt@kline_1m/thetausdt@kline_1m/filusdt@kline_1m/axsusdt@kline_1m/hbarusdt@kline_1m/vetusdt@kline_1m/icxusdt@kline_1m/chzusdt@kline_1m/manausdt@kline_1m/enjusdt@kline_1m/flowusdt@kline_1m/klayusdt@kline_1m/roseusdt@kline_1m/galausdt@kline_1m/gmtusdt@kline_1m/sushiusdt@kline_1m/1inchusdt@kline_1m/cakeusdt@kline_1m/zilusdt@kline_1m/hotusdt@kline_1m/balusdt@kline_1m/snxusdt@kline_1m/compusdt@kline_1m/celousdt@kline_1m/wavesusdt@kline_1m
[2025-07-24T21:04:56.962Z] [INFO] Connected to Binance Futures WebSocket for connection 1
[2025-07-24T21:04:56.964Z] [INFO] WebSocket connection 2 opened: wss://fstream.binance.com/ws/uniusdt@kline_1m/yfiusdt@kline_1m/dashusdt@kline_1m/zecusdt@kline_1m/batusdt@kline_1m/crvusdt@kline_1m/scusdt@kline_1m/neousdt@kline_1m/egldusdt@kline_1m/sfpusdt@kline_1m/anclusdt@kline_1m/oneusdt@kline_1m/lrcusdt@kline_1m/runeusdt@kline_1m/mkrusdt@kline_1m/lunausdt@kline_1m/ksmusdt@kline_1m/xemusdt@kline_1m/iotausdt@kline_1m/xtzusdt@kline_1m/rvnusdt@kline_1m/kavausdt@kline_1m/ctkusdt@kline_1m/arusdt@kline_1m/zenusdt@kline_1m/sxpusdt@kline_1m/sklusdt@kline_1m/injusdt@kline_1m/stgusdt@kline_1m/dydxusdt@kline_1m/gmxusdt@kline_1m/opulusdt@kline_1m/blurusdt@kline_1m/rndrusdt@kline_1m/ldousdt@kline_1m/snmusdt@kline_1m/agixusdt@kline_1m/radusdt@kline_1m/arusdt@kline_1m/beamusdt@kline_1m/imusdt@kline_1m/tusdt@kline_1m/joeusdt@kline_1m/achusdt@kline_1m/gftusdt@kline_1m/cotiusdt@kline_1m/idexusdt@kline_1m/aliceusdt@kline_1m/superusdt@kline_1m/suiusdt@kline_1m/pythusdt@kline_1m/wldusdt@kline_1m/arkmusdt@kline_1m/onetusdt@kline_1m/wusdt@kline_1m/pendleusdt@kline_1m/tiausdt@kline_1m/bonkusdt@kline_1m/ensusdt@kline_1m/altusdt@kline_1m/maverickusdt@kline_1m/ckbusdt@kline_1m/hftusdt@kline_1m/bigtimeusdt@kline_1m/renderusdt@kline_1m/omusdt@kline_1m/jupusdt@kline_1m/wifusdt@kline_1m
[2025-07-24T21:04:56.964Z] [INFO] Connected to Binance Futures WebSocket for connection 2
[2025-07-24T21:05:08.766Z] [INFO] Received SIGINT, shutting down gracefully...
[2025-07-24T21:05:08.767Z] [INFO] Shutting down Binance Tick Collector System...
[2025-07-24T21:05:08.768Z] [INFO] Closing all WebSocket connections...
[2025-07-24T21:05:08.769Z] [INFO] Closing WebSocket connection 1
[2025-07-24T21:05:08.770Z] [INFO] Closing WebSocket connection 2
[2025-07-24T21:05:08.770Z] [INFO] Closing tick workers...
[2025-07-24T21:05:08.771Z] [INFO] Health server closed
[2025-07-24T21:05:08.788Z] [INFO] SQLite database closed: /Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/data/ticks-2025-07-25.sqlite
[2025-07-24T21:05:08.788Z] [INFO] All tick workers closed
[2025-07-24T21:05:08.788Z] [INFO] Binance Tick Collector System shut down successfully
[2025-07-24T21:09:54.980Z] [INFO] Starting Binance Tick Collector System...
[2025-07-24T21:09:54.981Z] [INFO] Initialized 115 symbols across 2 connections
[2025-07-24T21:09:54.981Z] [INFO] Starting kline collector...
[2025-07-24T21:09:55.007Z] [INFO] SQLite database initialized: /Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/data/ticks-2025-07-25.sqlite
[2025-07-24T21:09:55.008Z] [INFO] Starting 10 tick workers...
[2025-07-24T21:09:55.010Z] [INFO] Started 10 tick workers
[2025-07-24T21:09:55.012Z] [INFO] Binance Tick Collector System started successfully
[2025-07-24T21:09:55.012Z] [INFO] === System Information ===
[2025-07-24T21:09:55.012Z] [INFO] CPU Cores: 10
[2025-07-24T21:09:55.012Z] [INFO] Total Memory: 32 GB
[2025-07-24T21:09:55.013Z] [INFO] Platform: darwin arm64
[2025-07-24T21:09:55.013Z] [INFO] Node.js Version: v19.9.0
[2025-07-24T21:09:55.013Z] [INFO] ========================
[2025-07-24T21:09:55.014Z] [INFO] Health server listening on port 3000
[2025-07-24T21:09:55.085Z] [INFO] Redis connection established
[2025-07-24T21:09:55.085Z] [INFO] Redis connection ready
[2025-07-24T21:09:55.968Z] [INFO] WebSocket connection 2 opened: wss://fstream.binance.com/ws/uniusdt@kline_1m/yfiusdt@kline_1m/dashusdt@kline_1m/zecusdt@kline_1m/batusdt@kline_1m/crvusdt@kline_1m/scusdt@kline_1m/neousdt@kline_1m/egldusdt@kline_1m/sfpusdt@kline_1m/anclusdt@kline_1m/oneusdt@kline_1m/lrcusdt@kline_1m/runeusdt@kline_1m/mkrusdt@kline_1m/lunausdt@kline_1m/ksmusdt@kline_1m/xemusdt@kline_1m/iotausdt@kline_1m/xtzusdt@kline_1m/rvnusdt@kline_1m/kavausdt@kline_1m/ctkusdt@kline_1m/arusdt@kline_1m/zenusdt@kline_1m/sxpusdt@kline_1m/sklusdt@kline_1m/injusdt@kline_1m/stgusdt@kline_1m/dydxusdt@kline_1m/gmxusdt@kline_1m/opulusdt@kline_1m/blurusdt@kline_1m/rndrusdt@kline_1m/ldousdt@kline_1m/snmusdt@kline_1m/agixusdt@kline_1m/radusdt@kline_1m/arusdt@kline_1m/beamusdt@kline_1m/imusdt@kline_1m/tusdt@kline_1m/joeusdt@kline_1m/achusdt@kline_1m/gftusdt@kline_1m/cotiusdt@kline_1m/idexusdt@kline_1m/aliceusdt@kline_1m/superusdt@kline_1m/suiusdt@kline_1m/pythusdt@kline_1m/wldusdt@kline_1m/arkmusdt@kline_1m/onetusdt@kline_1m/wusdt@kline_1m/pendleusdt@kline_1m/tiausdt@kline_1m/bonkusdt@kline_1m/ensusdt@kline_1m/altusdt@kline_1m/maverickusdt@kline_1m/ckbusdt@kline_1m/hftusdt@kline_1m/bigtimeusdt@kline_1m/renderusdt@kline_1m/omusdt@kline_1m/jupusdt@kline_1m/wifusdt@kline_1m
[2025-07-24T21:09:55.968Z] [INFO] Connected to Binance Futures WebSocket for connection 2
[2025-07-24T21:09:55.969Z] [INFO] WebSocket connection 1 opened: wss://fstream.binance.com/ws/btcusdt@kline_1m/ethusdt@kline_1m/bnbusdt@kline_1m/xrpusdt@kline_1m/adausdt@kline_1m/solusdt@kline_1m/dogeusdt@kline_1m/dotusdt@kline_1m/maticusdt@kline_1m/ltcusdt@kline_1m/linkusdt@kline_1m/trxusdt@kline_1m/etcusdt@kline_1m/icpusdt@kline_1m/nearusdt@kline_1m/atomusdt@kline_1m/xlmusdt@kline_1m/bchusdt@kline_1m/ftmusdt@kline_1m/apeusdt@kline_1m/sandusdt@kline_1m/eosusdt@kline_1m/aaveusdt@kline_1m/algousdt@kline_1m/thetausdt@kline_1m/filusdt@kline_1m/axsusdt@kline_1m/hbarusdt@kline_1m/vetusdt@kline_1m/icxusdt@kline_1m/chzusdt@kline_1m/manausdt@kline_1m/enjusdt@kline_1m/flowusdt@kline_1m/klayusdt@kline_1m/roseusdt@kline_1m/galausdt@kline_1m/gmtusdt@kline_1m/sushiusdt@kline_1m/1inchusdt@kline_1m/cakeusdt@kline_1m/zilusdt@kline_1m/hotusdt@kline_1m/balusdt@kline_1m/snxusdt@kline_1m/compusdt@kline_1m/celousdt@kline_1m/wavesusdt@kline_1m
[2025-07-24T21:09:55.969Z] [INFO] Connected to Binance Futures WebSocket for connection 1
[2025-07-24T21:09:56.009Z] [DEBUG] Job xrpusdt-1753391340000-1753391396006 is waiting
[2025-07-24T21:09:56.010Z] [DEBUG] Enqueued kline for xrpusdt at 1753391340000
[2025-07-24T21:09:56.010Z] [DEBUG] Enqueued kline for bnbusdt at 1753391340000
[2025-07-24T21:09:56.012Z] [DEBUG] Job bnbusdt-1753391340000-1753391396008 is waiting
[2025-07-24T21:09:56.012Z] [DEBUG] Job xrpusdt-1753391340000-1753391396006 is now active (prev: waiting)
[2025-07-24T21:09:56.013Z] [DEBUG] Worker completed job xrpusdt-1753391340000-1753391396006
[2025-07-24T21:09:56.013Z] [DEBUG] Job bnbusdt-1753391340000-1753391396008 is now active (prev: waiting)
[2025-07-24T21:09:56.014Z] [DEBUG] Job xrpusdt-1753391340000-1753391396006 completed with return value: [object Object]
[2025-07-24T21:09:56.014Z] [DEBUG] Worker completed job bnbusdt-1753391340000-1753391396008
[2025-07-24T21:09:56.014Z] [DEBUG] Job bnbusdt-1753391340000-1753391396008 completed with return value: [object Object]
[2025-07-24T21:09:56.052Z] [DEBUG] Job arusdt-1753391340000-1753391396052 is waiting
[2025-07-24T21:09:56.053Z] [DEBUG] Enqueued kline for arusdt at 1753391340000
[2025-07-24T21:09:56.054Z] [DEBUG] Job arusdt-1753391340000-1753391396052 is now active (prev: waiting)
[2025-07-24T21:09:56.054Z] [DEBUG] Job arusdt-1753391340000-1753391396052 completed with return value: [object Object]
[2025-07-24T21:09:56.055Z] [DEBUG] Worker completed job arusdt-1753391340000-1753391396052
[2025-07-24T21:09:56.140Z] [DEBUG] Job ksmusdt-1753391340000-1753391396139 is waiting
[2025-07-24T21:09:56.141Z] [DEBUG] Enqueued kline for ksmusdt at 1753391340000
[2025-07-24T21:09:56.142Z] [DEBUG] Job ksmusdt-1753391340000-1753391396139 is now active (prev: waiting)
[2025-07-24T21:09:56.143Z] [DEBUG] Worker completed job ksmusdt-1753391340000-1753391396139
[2025-07-24T21:09:56.143Z] [DEBUG] Job ksmusdt-1753391340000-1753391396139 completed with return value: [object Object]
[2025-07-24T21:09:56.164Z] [DEBUG] Job solusdt-1753391340000-1753391396162 is waiting
[2025-07-24T21:09:56.164Z] [DEBUG] Enqueued kline for solusdt at 1753391340000
[2025-07-24T21:09:56.165Z] [DEBUG] Job solusdt-1753391340000-1753391396162 is now active (prev: waiting)
[2025-07-24T21:09:56.166Z] [DEBUG] Job solusdt-1753391340000-1753391396162 completed with return value: [object Object]
[2025-07-24T21:09:56.166Z] [DEBUG] Worker completed job solusdt-1753391340000-1753391396162
[2025-07-24T21:09:56.243Z] [DEBUG] Job icxusdt-1753391340000-1753391396241 is waiting
[2025-07-24T21:09:56.243Z] [DEBUG] Enqueued kline for icxusdt at 1753391340000
[2025-07-24T21:09:56.244Z] [DEBUG] Job icxusdt-1753391340000-1753391396241 is now active (prev: waiting)
[2025-07-24T21:09:56.245Z] [DEBUG] Worker completed job icxusdt-1753391340000-1753391396241
[2025-07-24T21:09:56.245Z] [DEBUG] Job icxusdt-1753391340000-1753391396241 completed with return value: [object Object]
[2025-07-24T21:09:56.294Z] [DEBUG] Job adausdt-1753391340000-1753391396292 is waiting
[2025-07-24T21:09:56.294Z] [DEBUG] Enqueued kline for adausdt at 1753391340000
[2025-07-24T21:09:56.294Z] [DEBUG] Job adausdt-1753391340000-1753391396292 is now active (prev: waiting)
[2025-07-24T21:09:56.295Z] [DEBUG] Job adausdt-1753391340000-1753391396292 completed with return value: [object Object]
[2025-07-24T21:09:56.296Z] [DEBUG] Worker completed job adausdt-1753391340000-1753391396292
[2025-07-24T21:09:56.325Z] [DEBUG] Job axsusdt-1753391340000-1753391396324 is waiting
[2025-07-24T21:09:56.326Z] [DEBUG] Enqueued kline for axsusdt at 1753391340000
[2025-07-24T21:09:56.327Z] [DEBUG] Job axsusdt-1753391340000-1753391396324 is now active (prev: waiting)
[2025-07-24T21:09:56.327Z] [DEBUG] Worker completed job axsusdt-1753391340000-1753391396324
[2025-07-24T21:09:56.328Z] [DEBUG] Job axsusdt-1753391340000-1753391396324 completed with return value: [object Object]
[2025-07-24T21:09:56.368Z] [DEBUG] Job btcusdt-1753391340000-1753391396366 is waiting
[2025-07-24T21:09:56.368Z] [DEBUG] Enqueued kline for btcusdt at 1753391340000
[2025-07-24T21:09:56.369Z] [DEBUG] Job btcusdt-1753391340000-1753391396366 is now active (prev: waiting)
[2025-07-24T21:09:56.370Z] [DEBUG] Worker completed job btcusdt-1753391340000-1753391396366
[2025-07-24T21:09:56.370Z] [DEBUG] Job btcusdt-1753391340000-1753391396366 completed with return value: [object Object]
[2025-07-24T21:09:56.462Z] [DEBUG] Job hftusdt-1753391340000-1753391396459 is waiting
[2025-07-24T21:09:56.464Z] [DEBUG] Enqueued kline for hftusdt at 1753391340000
[2025-07-24T21:09:56.466Z] [DEBUG] Job hftusdt-1753391340000-1753391396459 is now active (prev: waiting)
[2025-07-24T21:09:56.467Z] [DEBUG] Worker completed job hftusdt-1753391340000-1753391396459
[2025-07-24T21:09:56.467Z] [DEBUG] Job hftusdt-1753391340000-1753391396459 completed with return value: [object Object]
[2025-07-24T21:09:56.472Z] [DEBUG] Job solusdt-1753391340000-1753391396471 is waiting
[2025-07-24T21:09:56.473Z] [DEBUG] Enqueued kline for solusdt at 1753391340000
[2025-07-24T21:09:56.474Z] [DEBUG] Job solusdt-1753391340000-1753391396471 is now active (prev: waiting)
[2025-07-24T21:09:56.474Z] [DEBUG] Worker completed job solusdt-1753391340000-1753391396471
[2025-07-24T21:09:56.474Z] [DEBUG] Job solusdt-1753391340000-1753391396471 completed with return value: [object Object]
[2025-07-24T21:09:56.549Z] [DEBUG] Job oneusdt-1753391340000-1753391396547 is waiting
[2025-07-24T21:09:56.550Z] [DEBUG] Enqueued kline for oneusdt at 1753391340000
[2025-07-24T21:09:56.551Z] [DEBUG] Job oneusdt-1753391340000-1753391396547 is now active (prev: waiting)
[2025-07-24T21:09:56.551Z] [DEBUG] Worker completed job oneusdt-1753391340000-1753391396547
[2025-07-24T21:09:56.552Z] [DEBUG] Job oneusdt-1753391340000-1753391396547 completed with return value: [object Object]
[2025-07-24T21:09:56.568Z] [DEBUG] Job thetausdt-1753391340000-1753391396567 is waiting
[2025-07-24T21:09:56.568Z] [DEBUG] Enqueued kline for thetausdt at 1753391340000
[2025-07-24T21:09:56.569Z] [DEBUG] Job thetausdt-1753391340000-1753391396567 is now active (prev: waiting)
[2025-07-24T21:09:56.572Z] [DEBUG] Job thetausdt-1753391340000-1753391396567 completed with return value: [object Object]
[2025-07-24T21:09:56.572Z] [DEBUG] Worker completed job thetausdt-1753391340000-1753391396567
[2025-07-24T21:09:56.601Z] [DEBUG] Job omusdt-1753391340000-1753391396599 is waiting
[2025-07-24T21:09:56.604Z] [DEBUG] Enqueued kline for omusdt at 1753391340000
[2025-07-24T21:09:56.604Z] [DEBUG] Job omusdt-1753391340000-1753391396599 is now active (prev: waiting)
[2025-07-24T21:09:56.605Z] [DEBUG] Worker completed job omusdt-1753391340000-1753391396599
[2025-07-24T21:09:56.605Z] [DEBUG] Job omusdt-1753391340000-1753391396599 completed with return value: [object Object]
[2025-07-24T21:09:56.628Z] [DEBUG] Job aaveusdt-1753391340000-1753391396625 is waiting
[2025-07-24T21:09:56.628Z] [DEBUG] Enqueued kline for aaveusdt at 1753391340000
[2025-07-24T21:09:56.629Z] [DEBUG] Job aaveusdt-1753391340000-1753391396625 is now active (prev: waiting)
[2025-07-24T21:09:56.630Z] [DEBUG] Worker completed job aaveusdt-1753391340000-1753391396625
[2025-07-24T21:09:56.630Z] [DEBUG] Job aaveusdt-1753391340000-1753391396625 completed with return value: [object Object]
[2025-07-24T21:09:56.651Z] [DEBUG] Job btcusdt-1753391340000-1753391396650 is waiting
[2025-07-24T21:09:56.652Z] [DEBUG] Enqueued kline for btcusdt at 1753391340000
[2025-07-24T21:09:56.652Z] [DEBUG] Job btcusdt-1753391340000-1753391396650 is now active (prev: waiting)
[2025-07-24T21:09:56.653Z] [DEBUG] Worker completed job btcusdt-1753391340000-1753391396650
[2025-07-24T21:09:56.654Z] [DEBUG] Job btcusdt-1753391340000-1753391396650 completed with return value: [object Object]
[2025-07-24T21:09:56.737Z] [DEBUG] Job injusdt-1753391340000-1753391396736 is waiting
[2025-07-24T21:09:56.738Z] [DEBUG] Enqueued kline for injusdt at 1753391340000
[2025-07-24T21:09:56.738Z] [DEBUG] Enqueued kline for xrpusdt at 1753391340000
[2025-07-24T21:09:56.739Z] [DEBUG] Job xrpusdt-1753391340000-1753391396737 is waiting
[2025-07-24T21:09:56.739Z] [DEBUG] Job injusdt-1753391340000-1753391396736 is now active (prev: waiting)
[2025-07-24T21:09:56.740Z] [DEBUG] Worker completed job injusdt-1753391340000-1753391396736
[2025-07-24T21:09:56.740Z] [DEBUG] Job xrpusdt-1753391340000-1753391396737 is now active (prev: waiting)
[2025-07-24T21:09:56.740Z] [DEBUG] Job injusdt-1753391340000-1753391396736 completed with return value: [object Object]
[2025-07-24T21:09:56.741Z] [DEBUG] Worker completed job xrpusdt-1753391340000-1753391396737
[2025-07-24T21:09:56.741Z] [DEBUG] Job xrpusdt-1753391340000-1753391396737 completed with return value: [object Object]
[2025-07-24T21:09:56.767Z] [DEBUG] Job bnbusdt-1753391340000-1753391396766 is waiting
[2025-07-24T21:09:56.768Z] [DEBUG] Enqueued kline for bnbusdt at 1753391340000
[2025-07-24T21:09:56.769Z] [DEBUG] Job bnbusdt-1753391340000-1753391396766 is now active (prev: waiting)
[2025-07-24T21:09:56.769Z] [DEBUG] Worker completed job bnbusdt-1753391340000-1753391396766
[2025-07-24T21:09:56.770Z] [DEBUG] Job bnbusdt-1753391340000-1753391396766 completed with return value: [object Object]
[2025-07-24T21:09:56.807Z] [DEBUG] Job solusdt-1753391340000-1753391396806 is waiting
[2025-07-24T21:09:56.807Z] [DEBUG] Enqueued kline for solusdt at 1753391340000
[2025-07-24T21:09:56.808Z] [DEBUG] Job solusdt-1753391340000-1753391396806 is now active (prev: waiting)
[2025-07-24T21:09:56.808Z] [DEBUG] Job solusdt-1753391340000-1753391396806 completed with return value: [object Object]
[2025-07-24T21:09:56.809Z] [DEBUG] Worker completed job solusdt-1753391340000-1753391396806
[2025-07-24T21:09:56.872Z] [DEBUG] Job ensusdt-1753391340000-1753391396867 is waiting
[2025-07-24T21:09:56.874Z] [DEBUG] Enqueued kline for ensusdt at 1753391340000
[2025-07-24T21:09:56.875Z] [DEBUG] Job ensusdt-1753391340000-1753391396867 is now active (prev: waiting)
[2025-07-24T21:09:56.876Z] [DEBUG] Job ensusdt-1753391340000-1753391396867 completed with return value: [object Object]
[2025-07-24T21:09:56.876Z] [DEBUG] Worker completed job ensusdt-1753391340000-1753391396867
[2025-07-24T21:09:56.877Z] [DEBUG] Job ethusdt-1753391340000-1753391396876 is waiting
[2025-07-24T21:09:56.877Z] [DEBUG] Enqueued kline for ethusdt at 1753391340000
[2025-07-24T21:09:56.878Z] [DEBUG] Job ethusdt-1753391340000-1753391396876 is now active (prev: waiting)
[2025-07-24T21:09:56.879Z] [DEBUG] Worker completed job ethusdt-1753391340000-1753391396876
[2025-07-24T21:09:56.879Z] [DEBUG] Job ethusdt-1753391340000-1753391396876 completed with return value: [object Object]
[2025-07-24T21:09:56.915Z] [DEBUG] Job ltcusdt-1753391340000-1753391396910 is waiting
[2025-07-24T21:09:56.916Z] [DEBUG] Enqueued kline for ltcusdt at 1753391340000
[2025-07-24T21:09:56.916Z] [DEBUG] Job ltcusdt-1753391340000-1753391396910 is now active (prev: waiting)
[2025-07-24T21:09:56.918Z] [DEBUG] Job ltcusdt-1753391340000-1753391396910 completed with return value: [object Object]
[2025-07-24T21:09:56.918Z] [DEBUG] Worker completed job ltcusdt-1753391340000-1753391396910
[2025-07-24T21:09:56.918Z] [DEBUG] Enqueued kline for wldusdt at 1753391340000
[2025-07-24T21:09:56.919Z] [DEBUG] Job wldusdt-1753391340000-1753391396918 is waiting
[2025-07-24T21:09:56.919Z] [DEBUG] Job wldusdt-1753391340000-1753391396918 is now active (prev: waiting)
[2025-07-24T21:09:56.921Z] [DEBUG] Job wldusdt-1753391340000-1753391396918 completed with return value: [object Object]
[2025-07-24T21:09:56.921Z] [DEBUG] Worker completed job wldusdt-1753391340000-1753391396918
[2025-07-24T21:09:56.923Z] [DEBUG] Job tiausdt-1753391340000-1753391396922 is waiting
[2025-07-24T21:09:56.923Z] [DEBUG] Enqueued kline for tiausdt at 1753391340000
[2025-07-24T21:09:56.924Z] [DEBUG] Worker completed job tiausdt-1753391340000-1753391396922
[2025-07-24T21:09:56.924Z] [DEBUG] Job tiausdt-1753391340000-1753391396922 is now active (prev: waiting)
[2025-07-24T21:09:56.925Z] [DEBUG] Job tiausdt-1753391340000-1753391396922 completed with return value: [object Object]
[2025-07-24T21:09:57.014Z] [DEBUG] Table created/verified for symbol: xrpusdt
[2025-07-24T21:09:57.018Z] [DEBUG] Inserted 2 klines for symbol xrpusdt
[2025-07-24T21:09:57.019Z] [DEBUG] Table created/verified for symbol: bnbusdt
[2025-07-24T21:09:57.019Z] [DEBUG] Inserted 2 klines for symbol bnbusdt
[2025-07-24T21:09:57.024Z] [DEBUG] Job cotiusdt-1753391340000-1753391397023 is waiting
[2025-07-24T21:09:57.025Z] [DEBUG] Enqueued kline for cotiusdt at 1753391340000
[2025-07-24T21:09:57.026Z] [DEBUG] Job cotiusdt-1753391340000-1753391397023 is now active (prev: waiting)
[2025-07-24T21:09:57.027Z] [DEBUG] Worker completed job cotiusdt-1753391340000-1753391397023
[2025-07-24T21:09:57.028Z] [DEBUG] Job cotiusdt-1753391340000-1753391397023 completed with return value: [object Object]
[2025-07-24T21:09:57.043Z] [DEBUG] Job jupusdt-1753391340000-1753391397042 is waiting
[2025-07-24T21:09:57.044Z] [DEBUG] Enqueued kline for jupusdt at 1753391340000
[2025-07-24T21:09:57.044Z] [DEBUG] Job jupusdt-1753391340000-1753391397042 is now active (prev: waiting)
[2025-07-24T21:09:57.045Z] [DEBUG] Worker completed job jupusdt-1753391340000-1753391397042
[2025-07-24T21:09:57.045Z] [DEBUG] Job jupusdt-1753391340000-1753391397042 completed with return value: [object Object]
[2025-07-24T21:09:57.054Z] [DEBUG] Table created/verified for symbol: arusdt
[2025-07-24T21:09:57.055Z] [DEBUG] Inserted 1 klines for symbol arusdt
[2025-07-24T21:09:57.072Z] [DEBUG] Job compusdt-1753391340000-1753391397072 is waiting
[2025-07-24T21:09:57.073Z] [DEBUG] Enqueued kline for compusdt at 1753391340000
[2025-07-24T21:09:57.073Z] [DEBUG] Enqueued kline for hotusdt at 1753391340000
[2025-07-24T21:09:57.073Z] [DEBUG] Job hotusdt-1753391340000-1753391397072 is waiting
[2025-07-24T21:09:57.073Z] [DEBUG] Job compusdt-1753391340000-1753391397072 is now active (prev: waiting)
[2025-07-24T21:09:57.073Z] [DEBUG] Job hotusdt-1753391340000-1753391397072 is now active (prev: waiting)
[2025-07-24T21:09:57.073Z] [DEBUG] Job compusdt-1753391340000-1753391397072 completed with return value: [object Object]
[2025-07-24T21:09:57.074Z] [DEBUG] Worker completed job compusdt-1753391340000-1753391397072
[2025-07-24T21:09:57.074Z] [DEBUG] Job hotusdt-1753391340000-1753391397072 completed with return value: [object Object]
[2025-07-24T21:09:57.074Z] [DEBUG] Worker completed job hotusdt-1753391340000-1753391397072
[2025-07-24T21:09:57.077Z] [DEBUG] Job xrpusdt-1753391340000-1753391397076 is waiting
[2025-07-24T21:09:57.077Z] [DEBUG] Enqueued kline for xrpusdt at 1753391340000
[2025-07-24T21:09:57.078Z] [DEBUG] Job xrpusdt-1753391340000-1753391397076 is now active (prev: waiting)
[2025-07-24T21:09:57.079Z] [DEBUG] Worker completed job xrpusdt-1753391340000-1753391397076
[2025-07-24T21:09:57.079Z] [DEBUG] Job xrpusdt-1753391340000-1753391397076 completed with return value: [object Object]
[2025-07-24T21:09:57.123Z] [DEBUG] Job thetausdt-1753391340000-1753391397122 is waiting
[2025-07-24T21:09:57.124Z] [DEBUG] Enqueued kline for thetausdt at 1753391340000
[2025-07-24T21:09:57.124Z] [DEBUG] Job thetausdt-1753391340000-1753391397122 is now active (prev: waiting)
[2025-07-24T21:09:57.124Z] [DEBUG] Worker completed job thetausdt-1753391340000-1753391397122
[2025-07-24T21:09:57.124Z] [DEBUG] Job thetausdt-1753391340000-1753391397122 completed with return value: [object Object]
[2025-07-24T21:09:57.143Z] [DEBUG] Table created/verified for symbol: ksmusdt
[2025-07-24T21:09:57.143Z] [DEBUG] Inserted 1 klines for symbol ksmusdt
[2025-07-24T21:09:57.160Z] [DEBUG] Job ethusdt-1753391340000-1753391397159 is waiting
[2025-07-24T21:09:57.160Z] [DEBUG] Enqueued kline for ethusdt at 1753391340000
[2025-07-24T21:09:57.161Z] [DEBUG] Job ethusdt-1753391340000-1753391397159 is now active (prev: waiting)
[2025-07-24T21:09:57.161Z] [DEBUG] Worker completed job ethusdt-1753391340000-1753391397159
[2025-07-24T21:09:57.162Z] [DEBUG] Job ethusdt-1753391340000-1753391397159 completed with return value: [object Object]
[2025-07-24T21:09:57.165Z] [DEBUG] Table created/verified for symbol: solusdt
[2025-07-24T21:09:57.166Z] [DEBUG] Inserted 3 klines for symbol solusdt
[2025-07-24T21:09:57.245Z] [DEBUG] Table created/verified for symbol: icxusdt
[2025-07-24T21:09:57.246Z] [DEBUG] Inserted 1 klines for symbol icxusdt
[2025-07-24T21:09:57.296Z] [DEBUG] Table created/verified for symbol: adausdt
[2025-07-24T21:09:57.297Z] [DEBUG] Inserted 1 klines for symbol adausdt
[2025-07-24T21:09:57.328Z] [DEBUG] Table created/verified for symbol: axsusdt
[2025-07-24T21:09:57.329Z] [DEBUG] Inserted 1 klines for symbol axsusdt
[2025-07-24T21:09:57.371Z] [DEBUG] Table created/verified for symbol: btcusdt
[2025-07-24T21:09:57.373Z] [DEBUG] Inserted 2 klines for symbol btcusdt
[2025-07-24T21:09:57.384Z] [DEBUG] Job dotusdt-1753391340000-1753391397382 is waiting
[2025-07-24T21:09:57.384Z] [DEBUG] Enqueued kline for dotusdt at 1753391340000
[2025-07-24T21:09:57.385Z] [DEBUG] Job dotusdt-1753391340000-1753391397382 is now active (prev: waiting)
[2025-07-24T21:09:57.386Z] [DEBUG] Worker completed job dotusdt-1753391340000-1753391397382
[2025-07-24T21:09:57.386Z] [DEBUG] Job dotusdt-1753391340000-1753391397382 completed with return value: [object Object]
[2025-07-24T21:09:57.456Z] [DEBUG] Job wifusdt-1753391340000-1753391397452 is waiting
[2025-07-24T21:09:57.457Z] [DEBUG] Enqueued kline for wifusdt at 1753391340000
[2025-07-24T21:09:57.458Z] [DEBUG] Job wifusdt-1753391340000-1753391397452 is now active (prev: waiting)
[2025-07-24T21:09:57.459Z] [DEBUG] Worker completed job wifusdt-1753391340000-1753391397452
[2025-07-24T21:09:57.459Z] [DEBUG] Job wifusdt-1753391340000-1753391397452 completed with return value: [object Object]
[2025-07-24T21:09:57.466Z] [DEBUG] Table created/verified for symbol: hftusdt
[2025-07-24T21:09:57.468Z] [DEBUG] Inserted 1 klines for symbol hftusdt
[2025-07-24T21:09:57.470Z] [DEBUG] Job bchusdt-1753391340000-1753391397469 is waiting
[2025-07-24T21:09:57.471Z] [DEBUG] Enqueued kline for bchusdt at 1753391340000
[2025-07-24T21:09:57.471Z] [DEBUG] Job bchusdt-1753391340000-1753391397469 is now active (prev: waiting)
[2025-07-24T21:09:57.472Z] [DEBUG] Job bchusdt-1753391340000-1753391397469 completed with return value: [object Object]
[2025-07-24T21:09:57.472Z] [DEBUG] Worker completed job bchusdt-1753391340000-1753391397469
[2025-07-24T21:09:57.551Z] [DEBUG] Table created/verified for symbol: oneusdt
[2025-07-24T21:09:57.554Z] [DEBUG] Inserted 1 klines for symbol oneusdt
[2025-07-24T21:09:57.570Z] [DEBUG] Table created/verified for symbol: thetausdt
[2025-07-24T21:09:57.574Z] [DEBUG] Inserted 2 klines for symbol thetausdt
[2025-07-24T21:09:57.605Z] [DEBUG] Table created/verified for symbol: omusdt
[2025-07-24T21:09:57.606Z] [DEBUG] Inserted 1 klines for symbol omusdt
[2025-07-24T21:09:57.630Z] [DEBUG] Table created/verified for symbol: aaveusdt
[2025-07-24T21:09:57.630Z] [DEBUG] Inserted 1 klines for symbol aaveusdt
[2025-07-24T21:09:57.660Z] [DEBUG] Job injusdt-1753391340000-1753391397659 is waiting
[2025-07-24T21:09:57.660Z] [DEBUG] Enqueued kline for injusdt at 1753391340000
[2025-07-24T21:09:57.661Z] [DEBUG] Job injusdt-1753391340000-1753391397659 is now active (prev: waiting)
[2025-07-24T21:09:57.661Z] [DEBUG] Job injusdt-1753391340000-1753391397659 completed with return value: [object Object]
[2025-07-24T21:09:57.661Z] [DEBUG] Worker completed job injusdt-1753391340000-1753391397659
[2025-07-24T21:09:57.718Z] [DEBUG] Job xtzusdt-1753391340000-1753391397717 is waiting
[2025-07-24T21:09:57.718Z] [DEBUG] Enqueued kline for xtzusdt at 1753391340000
[2025-07-24T21:09:57.718Z] [DEBUG] Job xtzusdt-1753391340000-1753391397717 is now active (prev: waiting)
[2025-07-24T21:09:57.720Z] [DEBUG] Job xtzusdt-1753391340000-1753391397717 completed with return value: [object Object]
[2025-07-24T21:09:57.720Z] [DEBUG] Worker completed job xtzusdt-1753391340000-1753391397717
[2025-07-24T21:09:57.740Z] [DEBUG] Table created/verified for symbol: injusdt
[2025-07-24T21:09:57.741Z] [DEBUG] Inserted 2 klines for symbol injusdt
[2025-07-24T21:09:57.766Z] [DEBUG] Job aaveusdt-1753391340000-1753391397763 is waiting
[2025-07-24T21:09:57.766Z] [DEBUG] Enqueued kline for aaveusdt at 1753391340000
[2025-07-24T21:09:57.767Z] [DEBUG] Job aaveusdt-1753391340000-1753391397763 is now active (prev: waiting)
[2025-07-24T21:09:57.767Z] [DEBUG] Worker completed job aaveusdt-1753391340000-1753391397763
[2025-07-24T21:09:57.767Z] [DEBUG] Job aaveusdt-1753391340000-1753391397763 completed with return value: [object Object]
[2025-07-24T21:09:57.782Z] [DEBUG] Job linkusdt-1753391340000-1753391397775 is waiting
[2025-07-24T21:09:57.783Z] [DEBUG] Enqueued kline for linkusdt at 1753391340000
[2025-07-24T21:09:57.784Z] [DEBUG] Job linkusdt-1753391340000-1753391397775 is now active (prev: waiting)
[2025-07-24T21:09:57.784Z] [DEBUG] Worker completed job linkusdt-1753391340000-1753391397775
[2025-07-24T21:09:57.785Z] [DEBUG] Job linkusdt-1753391340000-1753391397775 completed with return value: [object Object]
[2025-07-24T21:09:57.876Z] [DEBUG] Table created/verified for symbol: ensusdt
[2025-07-24T21:09:57.877Z] [DEBUG] Inserted 1 klines for symbol ensusdt
[2025-07-24T21:09:57.877Z] [DEBUG] Table created/verified for symbol: ethusdt
[2025-07-24T21:09:57.878Z] [DEBUG] Inserted 2 klines for symbol ethusdt
[2025-07-24T21:09:57.917Z] [DEBUG] Table created/verified for symbol: ltcusdt
[2025-07-24T21:09:57.917Z] [DEBUG] Inserted 1 klines for symbol ltcusdt
[2025-07-24T21:09:57.918Z] [DEBUG] Job lrcusdt-1753391340000-1753391397914 is waiting
[2025-07-24T21:09:57.918Z] [DEBUG] Enqueued kline for lrcusdt at 1753391340000
[2025-07-24T21:09:57.918Z] [DEBUG] Job lrcusdt-1753391340000-1753391397914 is now active (prev: waiting)
[2025-07-24T21:09:57.919Z] [DEBUG] Worker completed job lrcusdt-1753391340000-1753391397914
[2025-07-24T21:09:57.920Z] [DEBUG] Table created/verified for symbol: wldusdt
[2025-07-24T21:09:57.920Z] [DEBUG] Inserted 1 klines for symbol wldusdt
[2025-07-24T21:09:57.920Z] [DEBUG] Job lrcusdt-1753391340000-1753391397914 completed with return value: [object Object]
[2025-07-24T21:09:57.924Z] [DEBUG] Table created/verified for symbol: tiausdt
[2025-07-24T21:09:57.924Z] [DEBUG] Inserted 1 klines for symbol tiausdt
[2025-07-24T21:09:57.956Z] [DEBUG] Job wifusdt-1753391340000-1753391397955 is waiting
[2025-07-24T21:09:57.957Z] [DEBUG] Enqueued kline for wifusdt at 1753391340000
[2025-07-24T21:09:57.957Z] [DEBUG] Job wifusdt-1753391340000-1753391397955 is now active (prev: waiting)
[2025-07-24T21:09:57.958Z] [DEBUG] Worker completed job wifusdt-1753391340000-1753391397955
[2025-07-24T21:09:57.958Z] [DEBUG] Job wifusdt-1753391340000-1753391397955 completed with return value: [object Object]
[2025-07-24T21:09:57.974Z] [DEBUG] Job ltcusdt-1753391340000-1753391397973 is waiting
[2025-07-24T21:09:57.975Z] [DEBUG] Enqueued kline for ltcusdt at 1753391340000
[2025-07-24T21:09:57.975Z] [DEBUG] Job ltcusdt-1753391340000-1753391397973 is now active (prev: waiting)
[2025-07-24T21:09:57.977Z] [DEBUG] Job ltcusdt-1753391340000-1753391397973 completed with return value: [object Object]
[2025-07-24T21:09:57.977Z] [DEBUG] Worker completed job ltcusdt-1753391340000-1753391397973
[2025-07-24T21:09:58.013Z] [DEBUG] Job joeusdt-1753391340000-1753391398010 is waiting
[2025-07-24T21:09:58.015Z] [DEBUG] Enqueued kline for joeusdt at 1753391340000
[2025-07-24T21:09:58.016Z] [DEBUG] Job joeusdt-1753391340000-1753391398010 is now active (prev: waiting)
[2025-07-24T21:09:58.017Z] [DEBUG] Worker completed job joeusdt-1753391340000-1753391398010
[2025-07-24T21:09:58.017Z] [DEBUG] Job joeusdt-1753391340000-1753391398010 completed with return value: [object Object]
[2025-07-24T21:09:58.026Z] [DEBUG] Table created/verified for symbol: cotiusdt
[2025-07-24T21:09:58.027Z] [DEBUG] Inserted 1 klines for symbol cotiusdt
[2025-07-24T21:09:58.045Z] [DEBUG] Table created/verified for symbol: jupusdt
[2025-07-24T21:09:58.048Z] [DEBUG] Inserted 1 klines for symbol jupusdt
[2025-07-24T21:09:58.056Z] [DEBUG] Job solusdt-1753391340000-1753391398055 is waiting
[2025-07-24T21:09:58.057Z] [DEBUG] Enqueued kline for solusdt at 1753391340000
[2025-07-24T21:09:58.057Z] [DEBUG] Job solusdt-1753391340000-1753391398055 is now active (prev: waiting)
[2025-07-24T21:09:58.058Z] [DEBUG] Worker completed job solusdt-1753391340000-1753391398055
[2025-07-24T21:09:58.058Z] [DEBUG] Job solusdt-1753391340000-1753391398055 completed with return value: [object Object]
[2025-07-24T21:09:58.074Z] [DEBUG] Table created/verified for symbol: compusdt
[2025-07-24T21:09:58.075Z] [DEBUG] Inserted 1 klines for symbol compusdt
[2025-07-24T21:09:58.075Z] [DEBUG] Table created/verified for symbol: hotusdt
[2025-07-24T21:09:58.075Z] [DEBUG] Inserted 1 klines for symbol hotusdt
[2025-07-24T21:09:58.078Z] [DEBUG] Table created/verified for symbol: xrpusdt
[2025-07-24T21:09:58.078Z] [DEBUG] Inserted 1 klines for symbol xrpusdt
[2025-07-24T21:09:58.087Z] [DEBUG] Job neousdt-1753391340000-1753391398085 is waiting
[2025-07-24T21:09:58.087Z] [DEBUG] Enqueued kline for neousdt at 1753391340000
[2025-07-24T21:09:58.088Z] [DEBUG] Enqueued kline for hotusdt at 1753391340000
[2025-07-24T21:09:58.088Z] [DEBUG] Job hotusdt-1753391340000-1753391398086 is waiting
[2025-07-24T21:09:58.088Z] [DEBUG] Job neousdt-1753391340000-1753391398085 is now active (prev: waiting)
[2025-07-24T21:09:58.089Z] [DEBUG] Worker completed job neousdt-1753391340000-1753391398085
[2025-07-24T21:09:58.089Z] [DEBUG] Job hotusdt-1753391340000-1753391398086 is now active (prev: waiting)
[2025-07-24T21:09:58.089Z] [DEBUG] Job neousdt-1753391340000-1753391398085 completed with return value: [object Object]
[2025-07-24T21:09:58.090Z] [DEBUG] Worker completed job hotusdt-1753391340000-1753391398086
[2025-07-24T21:09:58.090Z] [DEBUG] Job hotusdt-1753391340000-1753391398086 completed with return value: [object Object]
[2025-07-24T21:09:58.103Z] [DEBUG] Job ethusdt-1753391340000-1753391398102 is waiting
[2025-07-24T21:09:58.103Z] [DEBUG] Enqueued kline for ethusdt at 1753391340000
[2025-07-24T21:09:58.103Z] [DEBUG] Job ethusdt-1753391340000-1753391398102 is now active (prev: waiting)
[2025-07-24T21:09:58.104Z] [DEBUG] Job ethusdt-1753391340000-1753391398102 completed with return value: [object Object]
[2025-07-24T21:09:58.104Z] [DEBUG] Worker completed job ethusdt-1753391340000-1753391398102
[2025-07-24T21:09:58.215Z] [DEBUG] Job sxpusdt-1753391340000-1753391398213 is waiting
[2025-07-24T21:09:58.217Z] [DEBUG] Enqueued kline for sxpusdt at 1753391340000
[2025-07-24T21:09:58.220Z] [DEBUG] Job sxpusdt-1753391340000-1753391398213 is now active (prev: waiting)
[2025-07-24T21:09:58.221Z] [DEBUG] Job sxpusdt-1753391340000-1753391398213 completed with return value: [object Object]
[2025-07-24T21:09:58.221Z] [DEBUG] Worker completed job sxpusdt-1753391340000-1753391398213
[2025-07-24T21:09:58.309Z] [DEBUG] Job btcusdt-1753391340000-1753391398306 is waiting
[2025-07-24T21:09:58.311Z] [DEBUG] Enqueued kline for btcusdt at 1753391340000
[2025-07-24T21:09:58.313Z] [DEBUG] Job btcusdt-1753391340000-1753391398306 is now active (prev: waiting)
[2025-07-24T21:09:58.314Z] [DEBUG] Job btcusdt-1753391340000-1753391398306 completed with return value: [object Object]
[2025-07-24T21:09:58.314Z] [DEBUG] Worker completed job btcusdt-1753391340000-1753391398306
[2025-07-24T21:09:58.385Z] [DEBUG] Table created/verified for symbol: dotusdt
[2025-07-24T21:09:58.386Z] [DEBUG] Inserted 1 klines for symbol dotusdt
[2025-07-24T21:09:58.459Z] [DEBUG] Table created/verified for symbol: wifusdt
[2025-07-24T21:09:58.460Z] [DEBUG] Inserted 2 klines for symbol wifusdt
[2025-07-24T21:09:58.472Z] [DEBUG] Table created/verified for symbol: bchusdt
[2025-07-24T21:09:58.473Z] [DEBUG] Inserted 1 klines for symbol bchusdt
[2025-07-24T21:09:58.484Z] [DEBUG] Job dydxusdt-1753391340000-1753391398480 is waiting
[2025-07-24T21:09:58.484Z] [DEBUG] Enqueued kline for dydxusdt at 1753391340000
[2025-07-24T21:09:58.485Z] [DEBUG] Job dydxusdt-1753391340000-1753391398480 is now active (prev: waiting)
[2025-07-24T21:09:58.486Z] [DEBUG] Worker completed job dydxusdt-1753391340000-1753391398480
[2025-07-24T21:09:58.486Z] [DEBUG] Job dydxusdt-1753391340000-1753391398480 completed with return value: [object Object]
[2025-07-24T21:09:58.493Z] [DEBUG] Job xrpusdt-1753391340000-1753391398492 is waiting
[2025-07-24T21:09:58.493Z] [DEBUG] Enqueued kline for xrpusdt at 1753391340000
[2025-07-24T21:09:58.495Z] [DEBUG] Job xrpusdt-1753391340000-1753391398492 is now active (prev: waiting)
[2025-07-24T21:09:58.496Z] [DEBUG] Worker completed job xrpusdt-1753391340000-1753391398492
[2025-07-24T21:09:58.496Z] [DEBUG] Job xrpusdt-1753391340000-1753391398492 completed with return value: [object Object]
[2025-07-24T21:09:58.534Z] [DEBUG] Job solusdt-1753391340000-1753391398532 is waiting
[2025-07-24T21:09:58.535Z] [DEBUG] Enqueued kline for solusdt at 1753391340000
[2025-07-24T21:09:58.536Z] [DEBUG] Job solusdt-1753391340000-1753391398532 is now active (prev: waiting)
[2025-07-24T21:09:58.537Z] [DEBUG] Worker completed job solusdt-1753391340000-1753391398532
[2025-07-24T21:09:58.537Z] [DEBUG] Job solusdt-1753391340000-1753391398532 completed with return value: [object Object]
[2025-07-24T21:09:58.575Z] [DEBUG] Job ethusdt-1753391340000-1753391398574 is waiting
[2025-07-24T21:09:58.576Z] [DEBUG] Enqueued kline for ethusdt at 1753391340000
[2025-07-24T21:09:58.576Z] [DEBUG] Job ethusdt-1753391340000-1753391398574 is now active (prev: waiting)
[2025-07-24T21:09:58.577Z] [DEBUG] Worker completed job ethusdt-1753391340000-1753391398574
[2025-07-24T21:09:58.577Z] [DEBUG] Job ethusdt-1753391340000-1753391398574 completed with return value: [object Object]
[2025-07-24T21:09:58.693Z] [DEBUG] Job btcusdt-1753391340000-1753391398692 is waiting
[2025-07-24T21:09:58.696Z] [DEBUG] Enqueued kline for btcusdt at 1753391340000
[2025-07-24T21:09:58.697Z] [DEBUG] Job btcusdt-1753391340000-1753391398692 is now active (prev: waiting)
[2025-07-24T21:09:58.698Z] [DEBUG] Worker completed job btcusdt-1753391340000-1753391398692
[2025-07-24T21:09:58.698Z] [DEBUG] Job btcusdt-1753391340000-1753391398692 completed with return value: [object Object]
[2025-07-24T21:09:58.711Z] [DEBUG] Job superusdt-1753391340000-1753391398710 is waiting
[2025-07-24T21:09:58.712Z] [DEBUG] Enqueued kline for superusdt at 1753391340000
[2025-07-24T21:09:58.712Z] [DEBUG] Job superusdt-1753391340000-1753391398710 is now active (prev: waiting)
[2025-07-24T21:09:58.713Z] [DEBUG] Worker completed job superusdt-1753391340000-1753391398710
[2025-07-24T21:09:58.713Z] [DEBUG] Job superusdt-1753391340000-1753391398710 completed with return value: [object Object]
[2025-07-24T21:09:58.720Z] [DEBUG] Table created/verified for symbol: xtzusdt
[2025-07-24T21:09:58.721Z] [DEBUG] Inserted 1 klines for symbol xtzusdt
[2025-07-24T21:09:58.768Z] [DEBUG] Table created/verified for symbol: aaveusdt
[2025-07-24T21:09:58.769Z] [DEBUG] Inserted 1 klines for symbol aaveusdt
[2025-07-24T21:09:58.784Z] [DEBUG] Table created/verified for symbol: linkusdt
[2025-07-24T21:09:58.784Z] [DEBUG] Inserted 1 klines for symbol linkusdt
[2025-07-24T21:09:58.919Z] [DEBUG] Table created/verified for symbol: lrcusdt
[2025-07-24T21:09:58.922Z] [DEBUG] Inserted 1 klines for symbol lrcusdt
[2025-07-24T21:09:58.976Z] [DEBUG] Table created/verified for symbol: ltcusdt
[2025-07-24T21:09:58.978Z] [DEBUG] Inserted 1 klines for symbol ltcusdt
[2025-07-24T21:09:58.995Z] [DEBUG] Job btcusdt-1753391340000-1753391398993 is waiting
[2025-07-24T21:09:58.995Z] [DEBUG] Enqueued kline for btcusdt at 1753391340000
[2025-07-24T21:09:58.996Z] [DEBUG] Job btcusdt-1753391340000-1753391398993 is now active (prev: waiting)
[2025-07-24T21:09:58.996Z] [DEBUG] Worker completed job btcusdt-1753391340000-1753391398993
[2025-07-24T21:09:58.997Z] [DEBUG] Job btcusdt-1753391340000-1753391398993 completed with return value: [object Object]
[2025-07-24T21:09:59.006Z] [DEBUG] Job solusdt-1753391340000-1753391399004 is waiting
[2025-07-24T21:09:59.007Z] [DEBUG] Enqueued kline for solusdt at 1753391340000
[2025-07-24T21:09:59.007Z] [DEBUG] Job solusdt-1753391340000-1753391399004 is now active (prev: waiting)
[2025-07-24T21:09:59.007Z] [DEBUG] Job solusdt-1753391340000-1753391399004 completed with return value: [object Object]
[2025-07-24T21:09:59.008Z] [DEBUG] Worker completed job solusdt-1753391340000-1753391399004
[2025-07-24T21:09:59.017Z] [DEBUG] Table created/verified for symbol: joeusdt
[2025-07-24T21:09:59.018Z] [DEBUG] Inserted 1 klines for symbol joeusdt
[2025-07-24T21:09:59.032Z] [DEBUG] Job ltcusdt-1753391340000-1753391399029 is waiting
[2025-07-24T21:09:59.032Z] [DEBUG] Enqueued kline for ltcusdt at 1753391340000
[2025-07-24T21:09:59.033Z] [DEBUG] Job ltcusdt-1753391340000-1753391399029 is now active (prev: waiting)
[2025-07-24T21:09:59.033Z] [DEBUG] Worker completed job ltcusdt-1753391340000-1753391399029
[2025-07-24T21:09:59.034Z] [DEBUG] Job ltcusdt-1753391340000-1753391399029 completed with return value: [object Object]
[2025-07-24T21:09:59.058Z] [DEBUG] Table created/verified for symbol: solusdt
[2025-07-24T21:09:59.059Z] [DEBUG] Inserted 3 klines for symbol solusdt
[2025-07-24T21:09:59.089Z] [DEBUG] Table created/verified for symbol: neousdt
[2025-07-24T21:09:59.090Z] [DEBUG] Inserted 1 klines for symbol neousdt
[2025-07-24T21:09:59.090Z] [DEBUG] Table created/verified for symbol: hotusdt
[2025-07-24T21:09:59.090Z] [DEBUG] Inserted 1 klines for symbol hotusdt
[2025-07-24T21:09:59.104Z] [DEBUG] Table created/verified for symbol: ethusdt
[2025-07-24T21:09:59.105Z] [DEBUG] Inserted 2 klines for symbol ethusdt
[2025-07-24T21:09:59.115Z] [DEBUG] Job filusdt-1753391340000-1753391399111 is waiting
[2025-07-24T21:09:59.115Z] [DEBUG] Enqueued kline for filusdt at 1753391340000
[2025-07-24T21:09:59.116Z] [DEBUG] Enqueued kline for ethusdt at 1753391340000
[2025-07-24T21:09:59.117Z] [DEBUG] Job ethusdt-1753391340000-1753391399112 is waiting
[2025-07-24T21:09:59.117Z] [DEBUG] Job filusdt-1753391340000-1753391399111 is now active (prev: waiting)
[2025-07-24T21:09:59.117Z] [DEBUG] Job ethusdt-1753391340000-1753391399112 is now active (prev: waiting)
[2025-07-24T21:09:59.118Z] [DEBUG] Worker completed job filusdt-1753391340000-1753391399111
[2025-07-24T21:09:59.118Z] [DEBUG] Worker completed job ethusdt-1753391340000-1753391399112
[2025-07-24T21:09:59.118Z] [DEBUG] Job filusdt-1753391340000-1753391399111 completed with return value: [object Object]
[2025-07-24T21:09:59.118Z] [DEBUG] Job ethusdt-1753391340000-1753391399112 completed with return value: [object Object]
[2025-07-24T21:09:59.196Z] [DEBUG] Job hotusdt-1753391340000-1753391399194 is waiting
[2025-07-24T21:09:59.197Z] [DEBUG] Enqueued kline for hotusdt at 1753391340000
[2025-07-24T21:09:59.197Z] [DEBUG] Enqueued kline for snxusdt at 1753391340000
[2025-07-24T21:09:59.198Z] [DEBUG] Job snxusdt-1753391340000-1753391399195 is waiting
[2025-07-24T21:09:59.198Z] [DEBUG] Job hotusdt-1753391340000-1753391399194 is now active (prev: waiting)
[2025-07-24T21:09:59.198Z] [DEBUG] Job snxusdt-1753391340000-1753391399195 is now active (prev: waiting)
[2025-07-24T21:09:59.198Z] [DEBUG] Job hotusdt-1753391340000-1753391399194 completed with return value: [object Object]
[2025-07-24T21:09:59.199Z] [DEBUG] Worker completed job hotusdt-1753391340000-1753391399194
[2025-07-24T21:09:59.199Z] [DEBUG] Job snxusdt-1753391340000-1753391399195 completed with return value: [object Object]
[2025-07-24T21:09:59.200Z] [DEBUG] Worker completed job snxusdt-1753391340000-1753391399195
[2025-07-24T21:09:59.203Z] [DEBUG] Job compusdt-1753391340000-1753391399201 is waiting
[2025-07-24T21:09:59.203Z] [DEBUG] Enqueued kline for compusdt at 1753391340000
[2025-07-24T21:09:59.204Z] [DEBUG] Enqueued kline for neousdt at 1753391340000
[2025-07-24T21:09:59.205Z] [DEBUG] Job neousdt-1753391340000-1753391399202 is waiting
[2025-07-24T21:09:59.205Z] [DEBUG] Job xrpusdt-1753391340000-1753391399203 is waiting
[2025-07-24T21:09:59.205Z] [DEBUG] Job compusdt-1753391340000-1753391399201 is now active (prev: waiting)
[2025-07-24T21:09:59.205Z] [DEBUG] Enqueued kline for xrpusdt at 1753391340000
[2025-07-24T21:09:59.206Z] [DEBUG] Job neousdt-1753391340000-1753391399202 is now active (prev: waiting)
[2025-07-24T21:09:59.206Z] [DEBUG] Worker completed job compusdt-1753391340000-1753391399201
[2025-07-24T21:09:59.206Z] [DEBUG] Worker completed job neousdt-1753391340000-1753391399202
[2025-07-24T21:09:59.207Z] [DEBUG] Worker completed job xrpusdt-1753391340000-1753391399203
[2025-07-24T21:09:59.207Z] [DEBUG] Job xrpusdt-1753391340000-1753391399203 is now active (prev: waiting)
[2025-07-24T21:09:59.207Z] [DEBUG] Job compusdt-1753391340000-1753391399201 completed with return value: [object Object]
[2025-07-24T21:09:59.207Z] [DEBUG] Job neousdt-1753391340000-1753391399202 completed with return value: [object Object]
[2025-07-24T21:09:59.207Z] [DEBUG] Job xrpusdt-1753391340000-1753391399203 completed with return value: [object Object]
[2025-07-24T21:09:59.220Z] [DEBUG] Table created/verified for symbol: sxpusdt
[2025-07-24T21:09:59.221Z] [DEBUG] Inserted 1 klines for symbol sxpusdt
[2025-07-24T21:09:59.269Z] [DEBUG] Job bchusdt-1753391340000-1753391399266 is waiting
[2025-07-24T21:09:59.269Z] [DEBUG] Enqueued kline for bchusdt at 1753391340000
[2025-07-24T21:09:59.270Z] [DEBUG] Job bchusdt-1753391340000-1753391399266 is now active (prev: waiting)
[2025-07-24T21:09:59.270Z] [DEBUG] Worker completed job bchusdt-1753391340000-1753391399266
[2025-07-24T21:09:59.270Z] [DEBUG] Job bchusdt-1753391340000-1753391399266 completed with return value: [object Object]
[2025-07-24T21:09:59.272Z] [INFO] Received SIGINT, shutting down gracefully...
[2025-07-24T21:09:59.272Z] [INFO] Shutting down Binance Tick Collector System...
[2025-07-24T21:09:59.273Z] [INFO] Closing all WebSocket connections...
[2025-07-24T21:09:59.273Z] [INFO] Closing WebSocket connection 1
[2025-07-24T21:09:59.273Z] [INFO] Closing WebSocket connection 2
[2025-07-24T21:09:59.274Z] [INFO] Closing tick workers...
[2025-07-24T21:09:59.274Z] [INFO] Health server closed
[2025-07-24T21:09:59.280Z] [DEBUG] Table created/verified for symbol: xrpusdt
[2025-07-24T21:09:59.280Z] [DEBUG] Inserted 2 klines for symbol xrpusdt
[2025-07-24T21:09:59.280Z] [DEBUG] Table created/verified for symbol: btcusdt
[2025-07-24T21:09:59.280Z] [DEBUG] Inserted 3 klines for symbol btcusdt
[2025-07-24T21:09:59.280Z] [DEBUG] Table created/verified for symbol: ethusdt
[2025-07-24T21:09:59.281Z] [DEBUG] Inserted 1 klines for symbol ethusdt
[2025-07-24T21:09:59.281Z] [DEBUG] Table created/verified for symbol: ltcusdt
[2025-07-24T21:09:59.281Z] [DEBUG] Inserted 1 klines for symbol ltcusdt
[2025-07-24T21:09:59.281Z] [DEBUG] Table created/verified for symbol: compusdt
[2025-07-24T21:09:59.281Z] [DEBUG] Inserted 1 klines for symbol compusdt
[2025-07-24T21:09:59.281Z] [DEBUG] Table created/verified for symbol: hotusdt
[2025-07-24T21:09:59.281Z] [DEBUG] Inserted 1 klines for symbol hotusdt
[2025-07-24T21:09:59.281Z] [DEBUG] Table created/verified for symbol: bchusdt
[2025-07-24T21:09:59.281Z] [DEBUG] Inserted 1 klines for symbol bchusdt
[2025-07-24T21:09:59.282Z] [DEBUG] Table created/verified for symbol: neousdt
[2025-07-24T21:09:59.282Z] [DEBUG] Inserted 1 klines for symbol neousdt
[2025-07-24T21:09:59.282Z] [DEBUG] Table created/verified for symbol: dydxusdt
[2025-07-24T21:09:59.282Z] [DEBUG] Inserted 1 klines for symbol dydxusdt
[2025-07-24T21:09:59.282Z] [DEBUG] Table created/verified for symbol: superusdt
[2025-07-24T21:09:59.282Z] [DEBUG] Inserted 1 klines for symbol superusdt
[2025-07-24T21:09:59.282Z] [DEBUG] Table created/verified for symbol: filusdt
[2025-07-24T21:09:59.282Z] [DEBUG] Inserted 1 klines for symbol filusdt
[2025-07-24T21:09:59.283Z] [DEBUG] Table created/verified for symbol: snxusdt
[2025-07-24T21:09:59.283Z] [DEBUG] Inserted 1 klines for symbol snxusdt
[2025-07-24T21:09:59.283Z] [INFO] SQLite database closed: /Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/data/ticks-2025-07-25.sqlite
[2025-07-24T21:09:59.283Z] [INFO] All tick workers closed
[2025-07-24T21:09:59.283Z] [INFO] Binance Tick Collector System shut down successfully
[2025-07-24T21:10:11.716Z] [INFO] Starting Binance Tick Collector System...
[2025-07-24T21:10:11.717Z] [INFO] Initialized 115 symbols across 2 connections
[2025-07-24T21:10:11.717Z] [INFO] Starting kline collector...
[2025-07-24T21:10:11.744Z] [INFO] SQLite database initialized: /Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/data/ticks-2025-07-25.sqlite
[2025-07-24T21:10:11.744Z] [INFO] Starting 10 tick workers...
[2025-07-24T21:10:11.746Z] [INFO] Started 10 tick workers
[2025-07-24T21:10:11.749Z] [INFO] Binance Tick Collector System started successfully
[2025-07-24T21:10:11.749Z] [INFO] === System Information ===
[2025-07-24T21:10:11.749Z] [INFO] CPU Cores: 10
[2025-07-24T21:10:11.749Z] [INFO] Total Memory: 32 GB
[2025-07-24T21:10:11.749Z] [INFO] Platform: darwin arm64
[2025-07-24T21:10:11.750Z] [INFO] Node.js Version: v19.9.0
[2025-07-24T21:10:11.750Z] [INFO] ========================
[2025-07-24T21:10:11.751Z] [INFO] Health server listening on port 3000
[2025-07-24T21:10:11.753Z] [INFO] Redis connection ready
[2025-07-24T21:10:12.644Z] [INFO] WebSocket connection 2 opened: wss://fstream.binance.com/ws/uniusdt@kline_1m/yfiusdt@kline_1m/dashusdt@kline_1m/zecusdt@kline_1m/batusdt@kline_1m/crvusdt@kline_1m/scusdt@kline_1m/neousdt@kline_1m/egldusdt@kline_1m/sfpusdt@kline_1m/anclusdt@kline_1m/oneusdt@kline_1m/lrcusdt@kline_1m/runeusdt@kline_1m/mkrusdt@kline_1m/lunausdt@kline_1m/ksmusdt@kline_1m/xemusdt@kline_1m/iotausdt@kline_1m/xtzusdt@kline_1m/rvnusdt@kline_1m/kavausdt@kline_1m/ctkusdt@kline_1m/arusdt@kline_1m/zenusdt@kline_1m/sxpusdt@kline_1m/sklusdt@kline_1m/injusdt@kline_1m/stgusdt@kline_1m/dydxusdt@kline_1m/gmxusdt@kline_1m/opulusdt@kline_1m/blurusdt@kline_1m/rndrusdt@kline_1m/ldousdt@kline_1m/snmusdt@kline_1m/agixusdt@kline_1m/radusdt@kline_1m/arusdt@kline_1m/beamusdt@kline_1m/imusdt@kline_1m/tusdt@kline_1m/joeusdt@kline_1m/achusdt@kline_1m/gftusdt@kline_1m/cotiusdt@kline_1m/idexusdt@kline_1m/aliceusdt@kline_1m/superusdt@kline_1m/suiusdt@kline_1m/pythusdt@kline_1m/wldusdt@kline_1m/arkmusdt@kline_1m/onetusdt@kline_1m/wusdt@kline_1m/pendleusdt@kline_1m/tiausdt@kline_1m/bonkusdt@kline_1m/ensusdt@kline_1m/altusdt@kline_1m/maverickusdt@kline_1m/ckbusdt@kline_1m/hftusdt@kline_1m/bigtimeusdt@kline_1m/renderusdt@kline_1m/omusdt@kline_1m/jupusdt@kline_1m/wifusdt@kline_1m
[2025-07-24T21:10:12.644Z] [INFO] Connected to Binance Futures WebSocket for connection 2
[2025-07-24T21:10:12.649Z] [INFO] WebSocket connection 1 opened: wss://fstream.binance.com/ws/btcusdt@kline_1m/ethusdt@kline_1m/bnbusdt@kline_1m/xrpusdt@kline_1m/adausdt@kline_1m/solusdt@kline_1m/dogeusdt@kline_1m/dotusdt@kline_1m/maticusdt@kline_1m/ltcusdt@kline_1m/linkusdt@kline_1m/trxusdt@kline_1m/etcusdt@kline_1m/icpusdt@kline_1m/nearusdt@kline_1m/atomusdt@kline_1m/xlmusdt@kline_1m/bchusdt@kline_1m/ftmusdt@kline_1m/apeusdt@kline_1m/sandusdt@kline_1m/eosusdt@kline_1m/aaveusdt@kline_1m/algousdt@kline_1m/thetausdt@kline_1m/filusdt@kline_1m/axsusdt@kline_1m/hbarusdt@kline_1m/vetusdt@kline_1m/icxusdt@kline_1m/chzusdt@kline_1m/manausdt@kline_1m/enjusdt@kline_1m/flowusdt@kline_1m/klayusdt@kline_1m/roseusdt@kline_1m/galausdt@kline_1m/gmtusdt@kline_1m/sushiusdt@kline_1m/1inchusdt@kline_1m/cakeusdt@kline_1m/zilusdt@kline_1m/hotusdt@kline_1m/balusdt@kline_1m/snxusdt@kline_1m/compusdt@kline_1m/celousdt@kline_1m/wavesusdt@kline_1m
[2025-07-24T21:10:12.649Z] [INFO] Connected to Binance Futures WebSocket for connection 1
[2025-07-24T21:42:05.989Z] [INFO] Received SIGINT, shutting down gracefully...
[2025-07-24T21:42:05.990Z] [INFO] Shutting down Binance Tick Collector System...
[2025-07-24T21:42:05.991Z] [INFO] Closing all WebSocket connections...
[2025-07-24T21:42:05.991Z] [INFO] Closing WebSocket connection 1
[2025-07-24T21:42:05.991Z] [INFO] Closing WebSocket connection 2
[2025-07-24T21:42:05.992Z] [INFO] Closing tick workers...
[2025-07-24T21:42:05.992Z] [INFO] Health server closed
[2025-07-24T21:42:06.009Z] [INFO] SQLite database closed: /Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/data/ticks-2025-07-25.sqlite
[2025-07-24T21:42:06.009Z] [INFO] All tick workers closed
[2025-07-24T21:42:06.009Z] [INFO] Binance Tick Collector System shut down successfully
[2025-07-24T21:44:11.489Z] [INFO] Starting Binance Tick Collector System...
[2025-07-24T21:44:11.490Z] [INFO] Initialized 50 symbols across 2 connections (limited to 25 pairs per connection)
[2025-07-24T21:44:11.490Z] [INFO] Starting kline collector...
[2025-07-24T21:44:11.519Z] [INFO] SQLite database initialized: /Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/data/ticks-2025-07-25.sqlite
[2025-07-24T21:44:11.520Z] [INFO] Starting 10 tick workers...
[2025-07-24T21:44:11.522Z] [INFO] Started 10 tick workers
[2025-07-24T21:44:11.524Z] [INFO] Binance Tick Collector System started successfully
[2025-07-24T21:44:11.524Z] [INFO] === System Information ===
[2025-07-24T21:44:11.524Z] [INFO] CPU Cores: 10
[2025-07-24T21:44:11.524Z] [INFO] Total Memory: 32 GB
[2025-07-24T21:44:11.524Z] [INFO] Platform: darwin arm64
[2025-07-24T21:44:11.525Z] [INFO] Node.js Version: v19.9.0
[2025-07-24T21:44:11.525Z] [INFO] ========================
[2025-07-24T21:44:11.526Z] [INFO] Health server listening on port 3000
[2025-07-24T21:44:11.603Z] [INFO] Redis connection ready
[2025-07-24T21:44:12.485Z] [INFO] WebSocket connection 1 opened: wss://fstream.binance.com/ws/btcusdt@kline_1m/ethusdt@kline_1m/bnbusdt@kline_1m/xrpusdt@kline_1m/adausdt@kline_1m/solusdt@kline_1m/dogeusdt@kline_1m/dotusdt@kline_1m/maticusdt@kline_1m/ltcusdt@kline_1m/linkusdt@kline_1m/trxusdt@kline_1m/etcusdt@kline_1m/icpusdt@kline_1m/nearusdt@kline_1m/atomusdt@kline_1m/xlmusdt@kline_1m/bchusdt@kline_1m/ftmusdt@kline_1m/apeusdt@kline_1m/sandusdt@kline_1m/eosusdt@kline_1m/aaveusdt@kline_1m/algousdt@kline_1m/thetausdt@kline_1m
[2025-07-24T21:44:12.485Z] [INFO] Connected to Binance Futures WebSocket for connection 1 with 25 pairs
[2025-07-24T21:44:12.489Z] [INFO] WebSocket connection 2 opened: wss://fstream.binance.com/ws/uniusdt@kline_1m/yfiusdt@kline_1m/dashusdt@kline_1m/zecusdt@kline_1m/batusdt@kline_1m/crvusdt@kline_1m/scusdt@kline_1m/neousdt@kline_1m/egldusdt@kline_1m/sfpusdt@kline_1m/anclusdt@kline_1m/oneusdt@kline_1m/lrcusdt@kline_1m/runeusdt@kline_1m/mkrusdt@kline_1m/lunausdt@kline_1m/ksmusdt@kline_1m/xemusdt@kline_1m/iotausdt@kline_1m/xtzusdt@kline_1m/rvnusdt@kline_1m/kavausdt@kline_1m/ctkusdt@kline_1m/arusdt@kline_1m/zenusdt@kline_1m
[2025-07-24T21:44:12.489Z] [INFO] Connected to Binance Futures WebSocket for connection 2 with 25 pairs
[2025-07-24T21:44:21.444Z] [INFO] Received SIGINT, shutting down gracefully...
[2025-07-24T21:44:21.445Z] [INFO] Shutting down Binance Tick Collector System...
[2025-07-24T21:44:21.446Z] [INFO] Closing all WebSocket connections...
[2025-07-24T21:44:21.446Z] [INFO] Closing WebSocket connection 1
[2025-07-24T21:44:21.448Z] [INFO] Closing WebSocket connection 2
[2025-07-24T21:44:21.448Z] [INFO] Closing tick workers...
[2025-07-24T21:44:21.448Z] [INFO] Health server closed
[2025-07-24T21:44:21.460Z] [INFO] SQLite database closed: /Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/data/ticks-2025-07-25.sqlite
[2025-07-24T21:44:21.461Z] [INFO] All tick workers closed
[2025-07-24T21:44:21.461Z] [INFO] Binance Tick Collector System shut down successfully
[2025-07-24T21:50:25.555Z] [INFO] Starting Binance Tick Collector System...
[2025-07-24T21:50:25.555Z] [INFO] Initialized 115 symbols across 3 connections (limited to 50 pairs per connection)
[2025-07-24T21:50:25.556Z] [INFO] Starting kline collector...
[2025-07-24T21:50:25.583Z] [INFO] SQLite database initialized: /Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/data/ticks-2025-07-25.sqlite
[2025-07-24T21:50:25.583Z] [INFO] Starting 10 tick workers...
[2025-07-24T21:50:25.585Z] [INFO] Started 10 tick workers
[2025-07-24T21:50:25.587Z] [INFO] Binance Tick Collector System started successfully
[2025-07-24T21:50:25.588Z] [INFO] === System Information ===
[2025-07-24T21:50:25.588Z] [INFO] CPU Cores: 10
[2025-07-24T21:50:25.588Z] [INFO] Total Memory: 32 GB
[2025-07-24T21:50:25.588Z] [INFO] Platform: darwin arm64
[2025-07-24T21:50:25.588Z] [INFO] Node.js Version: v19.9.0
[2025-07-24T21:50:25.588Z] [INFO] ========================
[2025-07-24T21:50:25.590Z] [INFO] Health server listening on port 3000
[2025-07-24T21:50:25.663Z] [INFO] Redis connection ready
[2025-07-24T21:50:26.747Z] [INFO] WebSocket connection 1 opened: wss://fstream.binance.com/ws/btcusdt@kline_1m/ethusdt@kline_1m/bnbusdt@kline_1m/xrpusdt@kline_1m/adausdt@kline_1m/solusdt@kline_1m/dogeusdt@kline_1m/dotusdt@kline_1m/maticusdt@kline_1m/ltcusdt@kline_1m/linkusdt@kline_1m/trxusdt@kline_1m/etcusdt@kline_1m/icpusdt@kline_1m/nearusdt@kline_1m/atomusdt@kline_1m/xlmusdt@kline_1m/bchusdt@kline_1m/ftmusdt@kline_1m/apeusdt@kline_1m/sandusdt@kline_1m/eosusdt@kline_1m/aaveusdt@kline_1m/algousdt@kline_1m/thetausdt@kline_1m/filusdt@kline_1m/axsusdt@kline_1m/hbarusdt@kline_1m/vetusdt@kline_1m/icxusdt@kline_1m/chzusdt@kline_1m/manausdt@kline_1m/enjusdt@kline_1m/flowusdt@kline_1m/klayusdt@kline_1m/roseusdt@kline_1m/galausdt@kline_1m/gmtusdt@kline_1m/sushiusdt@kline_1m/1inchusdt@kline_1m/cakeusdt@kline_1m/zilusdt@kline_1m/hotusdt@kline_1m/balusdt@kline_1m/snxusdt@kline_1m/compusdt@kline_1m/celousdt@kline_1m/wavesusdt@kline_1m/uniusdt@kline_1m/yfiusdt@kline_1m
[2025-07-24T21:50:26.748Z] [INFO] Connected to Binance Futures WebSocket for connection 1 with 50 pairs
[2025-07-24T21:50:26.749Z] [INFO] WebSocket connection 2 opened: wss://fstream.binance.com/ws/dashusdt@kline_1m/zecusdt@kline_1m/batusdt@kline_1m/crvusdt@kline_1m/scusdt@kline_1m/neousdt@kline_1m/egldusdt@kline_1m/sfpusdt@kline_1m/anclusdt@kline_1m/oneusdt@kline_1m/lrcusdt@kline_1m/runeusdt@kline_1m/mkrusdt@kline_1m/lunausdt@kline_1m/ksmusdt@kline_1m/xemusdt@kline_1m/iotausdt@kline_1m/xtzusdt@kline_1m/rvnusdt@kline_1m/kavausdt@kline_1m/ctkusdt@kline_1m/arusdt@kline_1m/zenusdt@kline_1m/sxpusdt@kline_1m/sklusdt@kline_1m/injusdt@kline_1m/stgusdt@kline_1m/dydxusdt@kline_1m/gmxusdt@kline_1m/opulusdt@kline_1m/blurusdt@kline_1m/rndrusdt@kline_1m/ldousdt@kline_1m/snmusdt@kline_1m/agixusdt@kline_1m/radusdt@kline_1m/arusdt@kline_1m/beamusdt@kline_1m/imusdt@kline_1m/tusdt@kline_1m/joeusdt@kline_1m/achusdt@kline_1m/gftusdt@kline_1m/cotiusdt@kline_1m/idexusdt@kline_1m/aliceusdt@kline_1m/superusdt@kline_1m/suiusdt@kline_1m/pythusdt@kline_1m/wldusdt@kline_1m
[2025-07-24T21:50:26.750Z] [INFO] Connected to Binance Futures WebSocket for connection 2 with 50 pairs
[2025-07-24T21:50:26.750Z] [INFO] WebSocket connection 3 opened: wss://fstream.binance.com/ws/arkmusdt@kline_1m/onetusdt@kline_1m/wusdt@kline_1m/pendleusdt@kline_1m/tiausdt@kline_1m/bonkusdt@kline_1m/ensusdt@kline_1m/altusdt@kline_1m/maverickusdt@kline_1m/ckbusdt@kline_1m/hftusdt@kline_1m/bigtimeusdt@kline_1m/renderusdt@kline_1m/omusdt@kline_1m/jupusdt@kline_1m/wifusdt@kline_1m
[2025-07-24T21:50:26.750Z] [INFO] Connected to Binance Futures WebSocket for connection 3 with 16 pairs
[2025-07-24T21:56:46.779Z] [WARN] WebSocket connection 1 timeout detected. Time since last data: 17104ms
[2025-07-24T21:56:46.786Z] [INFO] Reconnecting WebSocket connection 1...
[2025-07-24T21:56:46.794Z] [WARN] WebSocket connection 2 timeout detected. Time since last data: 17103ms
[2025-07-24T21:56:46.794Z] [INFO] Reconnecting WebSocket connection 2...
[2025-07-24T21:56:46.796Z] [WARN] WebSocket connection 3 timeout detected. Time since last data: 17611ms
[2025-07-24T21:56:46.796Z] [INFO] Reconnecting WebSocket connection 3...
[2025-07-24T21:56:56.780Z] [WARN] WebSocket connection 1 timeout detected. Time since last data: 27105ms
[2025-07-24T21:56:56.786Z] [INFO] Reconnecting WebSocket connection 1...
[2025-07-24T21:56:56.789Z] [ERROR] Uncaught Exception: WebSocket was closed before the connection was established
[2025-07-24T21:56:56.790Z] [ERROR] Error: WebSocket was closed before the connection was established
    at WebSocket.close (/Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/node_modules/ws/lib/websocket.js:299:7)
    at WebSocketMonitor.connect (/Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/websocket-monitor.js:31:22)
    at WebSocketMonitor.reconnect (/Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/websocket-monitor.js:154:10)
    at Timeout._onTimeout (/Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/websocket-monitor.js:113:14)
    at listOnTimeout (node:internal/timers:573:17)
    at process.processTimers (node:internal/timers:514:7)
[2025-07-24T21:56:56.790Z] [INFO] Shutting down Binance Tick Collector System...
[2025-07-24T21:56:56.790Z] [INFO] Closing all WebSocket connections...
[2025-07-24T21:56:56.791Z] [INFO] Closing WebSocket connection 1
[2025-07-24T21:56:56.791Z] [INFO] Closing WebSocket connection 2
[2025-07-24T21:56:56.791Z] [INFO] Closing WebSocket connection 3
[2025-07-24T21:56:56.792Z] [INFO] Closing tick workers...
[2025-07-24T21:56:56.793Z] [INFO] Health server closed
[2025-07-24T21:56:56.794Z] [ERROR] WebSocket connection 1 error: WebSocket was closed before the connection was established
[2025-07-24T21:56:56.794Z] [ERROR] WebSocket error for connection 1: WebSocket was closed before the connection was established
[2025-07-24T21:56:56.794Z] [WARN] WebSocket connection 1 closed. Code: 1006, Reason: 
[2025-07-24T21:56:56.794Z] [WARN] WebSocket closed for connection 1. Code: 1006, Reason: 
[2025-07-24T21:56:56.795Z] [INFO] Scheduling reconnect for WebSocket connection 1 in 5000ms (attempt 1)
[2025-07-24T21:56:56.795Z] [ERROR] WebSocket connection 2 error: WebSocket was closed before the connection was established
[2025-07-24T21:56:56.795Z] [ERROR] WebSocket error for connection 2: WebSocket was closed before the connection was established
[2025-07-24T21:56:56.795Z] [WARN] WebSocket connection 2 closed. Code: 1006, Reason: 
[2025-07-24T21:56:56.795Z] [WARN] WebSocket closed for connection 2. Code: 1006, Reason: 
[2025-07-24T21:56:56.795Z] [INFO] Scheduling reconnect for WebSocket connection 2 in 5000ms (attempt 1)
[2025-07-24T21:56:56.795Z] [ERROR] WebSocket connection 3 error: WebSocket was closed before the connection was established
[2025-07-24T21:56:56.795Z] [ERROR] WebSocket error for connection 3: WebSocket was closed before the connection was established
[2025-07-24T21:56:56.795Z] [WARN] WebSocket connection 3 closed. Code: 1006, Reason: 
[2025-07-24T21:56:56.795Z] [WARN] WebSocket closed for connection 3. Code: 1006, Reason: 
[2025-07-24T21:56:56.796Z] [INFO] Scheduling reconnect for WebSocket connection 3 in 5000ms (attempt 1)
[2025-07-24T21:56:56.806Z] [INFO] SQLite database closed: /Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/data/ticks-2025-07-25.sqlite
[2025-07-24T21:56:56.806Z] [INFO] All tick workers closed
[2025-07-24T21:56:56.806Z] [INFO] Binance Tick Collector System shut down successfully
[2025-07-24T21:57:33.872Z] [INFO] Starting Binance Tick Collector System...
[2025-07-24T21:57:33.872Z] [INFO] Initialized 115 symbols across 5 connections (limited to 25 pairs per connection)
[2025-07-24T21:57:33.872Z] [INFO] Starting kline collector...
[2025-07-24T21:57:33.903Z] [INFO] SQLite database initialized: /Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/data/ticks-2025-07-25.sqlite
[2025-07-24T21:57:33.903Z] [INFO] Starting 10 tick workers...
[2025-07-24T21:57:33.905Z] [INFO] Started 10 tick workers
[2025-07-24T21:57:33.907Z] [INFO] Binance Tick Collector System started successfully
[2025-07-24T21:57:33.908Z] [INFO] === System Information ===
[2025-07-24T21:57:33.908Z] [INFO] CPU Cores: 10
[2025-07-24T21:57:33.908Z] [INFO] Total Memory: 32 GB
[2025-07-24T21:57:33.908Z] [INFO] Platform: darwin arm64
[2025-07-24T21:57:33.908Z] [INFO] Node.js Version: v19.9.0
[2025-07-24T21:57:33.908Z] [INFO] ========================
[2025-07-24T21:57:33.910Z] [INFO] Health server listening on port 3000
[2025-07-24T21:57:41.863Z] [INFO] Redis connection ready
[2025-07-24T21:57:42.741Z] [INFO] WebSocket connection 1 opened: wss://fstream.binance.com/ws/btcusdt@kline_1m/ethusdt@kline_1m/bnbusdt@kline_1m/xrpusdt@kline_1m/adausdt@kline_1m/solusdt@kline_1m/dogeusdt@kline_1m/dotusdt@kline_1m/maticusdt@kline_1m/ltcusdt@kline_1m/linkusdt@kline_1m/trxusdt@kline_1m/etcusdt@kline_1m/icpusdt@kline_1m/nearusdt@kline_1m/atomusdt@kline_1m/xlmusdt@kline_1m/bchusdt@kline_1m/ftmusdt@kline_1m/apeusdt@kline_1m/sandusdt@kline_1m/eosusdt@kline_1m/aaveusdt@kline_1m/algousdt@kline_1m/thetausdt@kline_1m
[2025-07-24T21:57:42.741Z] [INFO] Connected to Binance Futures WebSocket for connection 1 with 25 pairs
[2025-07-24T21:57:42.743Z] [INFO] WebSocket connection 2 opened: wss://fstream.binance.com/ws/filusdt@kline_1m/axsusdt@kline_1m/hbarusdt@kline_1m/vetusdt@kline_1m/icxusdt@kline_1m/chzusdt@kline_1m/manausdt@kline_1m/enjusdt@kline_1m/flowusdt@kline_1m/klayusdt@kline_1m/roseusdt@kline_1m/galausdt@kline_1m/gmtusdt@kline_1m/sushiusdt@kline_1m/1inchusdt@kline_1m/cakeusdt@kline_1m/zilusdt@kline_1m/hotusdt@kline_1m/balusdt@kline_1m/snxusdt@kline_1m/compusdt@kline_1m/celousdt@kline_1m/wavesusdt@kline_1m/uniusdt@kline_1m/yfiusdt@kline_1m
[2025-07-24T21:57:42.744Z] [INFO] Connected to Binance Futures WebSocket for connection 2 with 25 pairs
[2025-07-24T21:57:42.758Z] [INFO] WebSocket connection 4 opened: wss://fstream.binance.com/ws/injusdt@kline_1m/stgusdt@kline_1m/dydxusdt@kline_1m/gmxusdt@kline_1m/opulusdt@kline_1m/blurusdt@kline_1m/rndrusdt@kline_1m/ldousdt@kline_1m/snmusdt@kline_1m/agixusdt@kline_1m/radusdt@kline_1m/arusdt@kline_1m/beamusdt@kline_1m/imusdt@kline_1m/tusdt@kline_1m/joeusdt@kline_1m/achusdt@kline_1m/gftusdt@kline_1m/cotiusdt@kline_1m/idexusdt@kline_1m/aliceusdt@kline_1m/superusdt@kline_1m/suiusdt@kline_1m/pythusdt@kline_1m/wldusdt@kline_1m
[2025-07-24T21:57:42.758Z] [INFO] Connected to Binance Futures WebSocket for connection 4 with 25 pairs
[2025-07-24T21:57:42.758Z] [INFO] WebSocket connection 3 opened: wss://fstream.binance.com/ws/dashusdt@kline_1m/zecusdt@kline_1m/batusdt@kline_1m/crvusdt@kline_1m/scusdt@kline_1m/neousdt@kline_1m/egldusdt@kline_1m/sfpusdt@kline_1m/anclusdt@kline_1m/oneusdt@kline_1m/lrcusdt@kline_1m/runeusdt@kline_1m/mkrusdt@kline_1m/lunausdt@kline_1m/ksmusdt@kline_1m/xemusdt@kline_1m/iotausdt@kline_1m/xtzusdt@kline_1m/rvnusdt@kline_1m/kavausdt@kline_1m/ctkusdt@kline_1m/arusdt@kline_1m/zenusdt@kline_1m/sxpusdt@kline_1m/sklusdt@kline_1m
[2025-07-24T21:57:42.758Z] [INFO] Connected to Binance Futures WebSocket for connection 3 with 25 pairs
[2025-07-24T21:57:42.758Z] [INFO] WebSocket connection 5 opened: wss://fstream.binance.com/ws/arkmusdt@kline_1m/onetusdt@kline_1m/wusdt@kline_1m/pendleusdt@kline_1m/tiausdt@kline_1m/bonkusdt@kline_1m/ensusdt@kline_1m/altusdt@kline_1m/maverickusdt@kline_1m/ckbusdt@kline_1m/hftusdt@kline_1m/bigtimeusdt@kline_1m/renderusdt@kline_1m/omusdt@kline_1m/jupusdt@kline_1m/wifusdt@kline_1m
[2025-07-24T21:57:42.758Z] [INFO] Connected to Binance Futures WebSocket for connection 5 with 16 pairs
[2025-07-24T22:04:15.411Z] [INFO] Received SIGINT, shutting down gracefully...
[2025-07-24T22:04:15.414Z] [INFO] Shutting down Binance Tick Collector System...
[2025-07-24T22:04:15.415Z] [INFO] Closing all WebSocket connections...
[2025-07-24T22:04:15.415Z] [INFO] Closing WebSocket connection 1
[2025-07-24T22:04:15.416Z] [INFO] Closing WebSocket connection 2
[2025-07-24T22:04:15.417Z] [INFO] Closing WebSocket connection 3
[2025-07-24T22:04:15.417Z] [INFO] Closing WebSocket connection 4
[2025-07-24T22:04:15.417Z] [INFO] Closing WebSocket connection 5
[2025-07-24T22:04:15.418Z] [INFO] Closing tick workers...
[2025-07-24T22:04:15.418Z] [INFO] Health server closed
[2025-07-24T22:04:15.432Z] [INFO] SQLite database closed: /Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/data/ticks-2025-07-25.sqlite
[2025-07-24T22:04:15.432Z] [INFO] All tick workers closed
[2025-07-24T22:04:15.432Z] [INFO] Binance Tick Collector System shut down successfully
[2025-07-25T05:40:36.069Z] [INFO] Starting Binance Tick Collector System...
[2025-07-25T05:40:36.069Z] [INFO] Initialized 115 symbols across 5 connections (limited to 25 pairs per connection)
[2025-07-25T05:40:36.070Z] [INFO] Starting kline collector...
[2025-07-25T05:40:36.106Z] [INFO] SQLite database initialized: /Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/data/ticks-2025-07-25.sqlite
[2025-07-25T05:40:36.106Z] [INFO] Starting 10 tick workers...
[2025-07-25T05:40:36.108Z] [INFO] Started 10 tick workers
[2025-07-25T05:40:36.110Z] [INFO] Binance Tick Collector System started successfully
[2025-07-25T05:40:36.111Z] [INFO] === System Information ===
[2025-07-25T05:40:36.111Z] [INFO] CPU Cores: 10
[2025-07-25T05:40:36.111Z] [INFO] Total Memory: 32 GB
[2025-07-25T05:40:36.111Z] [INFO] Platform: darwin arm64
[2025-07-25T05:40:36.111Z] [INFO] Node.js Version: v19.9.0
[2025-07-25T05:40:36.111Z] [INFO] ========================
[2025-07-25T05:40:36.113Z] [INFO] Health server listening on port 3000
[2025-07-25T05:40:37.428Z] [INFO] Redis connection ready
[2025-07-25T05:40:38.535Z] [INFO] WebSocket connection 1 opened: wss://fstream.binance.com/ws/btcusdt@kline_1m/ethusdt@kline_1m/bnbusdt@kline_1m/xrpusdt@kline_1m/adausdt@kline_1m/solusdt@kline_1m/dogeusdt@kline_1m/dotusdt@kline_1m/maticusdt@kline_1m/ltcusdt@kline_1m/linkusdt@kline_1m/trxusdt@kline_1m/etcusdt@kline_1m/icpusdt@kline_1m/nearusdt@kline_1m/atomusdt@kline_1m/xlmusdt@kline_1m/bchusdt@kline_1m/ftmusdt@kline_1m/apeusdt@kline_1m/sandusdt@kline_1m/eosusdt@kline_1m/aaveusdt@kline_1m/algousdt@kline_1m/thetausdt@kline_1m
[2025-07-25T05:40:38.535Z] [INFO] Connected to Binance Futures WebSocket for connection 1 with 25 pairs
[2025-07-25T05:40:38.542Z] [INFO] WebSocket connection 2 opened: wss://fstream.binance.com/ws/filusdt@kline_1m/axsusdt@kline_1m/hbarusdt@kline_1m/vetusdt@kline_1m/icxusdt@kline_1m/chzusdt@kline_1m/manausdt@kline_1m/enjusdt@kline_1m/flowusdt@kline_1m/klayusdt@kline_1m/roseusdt@kline_1m/galausdt@kline_1m/gmtusdt@kline_1m/sushiusdt@kline_1m/1inchusdt@kline_1m/cakeusdt@kline_1m/zilusdt@kline_1m/hotusdt@kline_1m/balusdt@kline_1m/snxusdt@kline_1m/compusdt@kline_1m/celousdt@kline_1m/wavesusdt@kline_1m/uniusdt@kline_1m/yfiusdt@kline_1m
[2025-07-25T05:40:38.542Z] [INFO] Connected to Binance Futures WebSocket for connection 2 with 25 pairs
[2025-07-25T05:40:38.616Z] [INFO] WebSocket connection 5 opened: wss://fstream.binance.com/ws/arkmusdt@kline_1m/onetusdt@kline_1m/wusdt@kline_1m/pendleusdt@kline_1m/tiausdt@kline_1m/bonkusdt@kline_1m/ensusdt@kline_1m/altusdt@kline_1m/maverickusdt@kline_1m/ckbusdt@kline_1m/hftusdt@kline_1m/bigtimeusdt@kline_1m/renderusdt@kline_1m/omusdt@kline_1m/jupusdt@kline_1m/wifusdt@kline_1m
[2025-07-25T05:40:38.616Z] [INFO] Connected to Binance Futures WebSocket for connection 5 with 16 pairs
[2025-07-25T05:40:38.673Z] [INFO] WebSocket connection 4 opened: wss://fstream.binance.com/ws/injusdt@kline_1m/stgusdt@kline_1m/dydxusdt@kline_1m/gmxusdt@kline_1m/opulusdt@kline_1m/blurusdt@kline_1m/rndrusdt@kline_1m/ldousdt@kline_1m/snmusdt@kline_1m/agixusdt@kline_1m/radusdt@kline_1m/arusdt@kline_1m/beamusdt@kline_1m/imusdt@kline_1m/tusdt@kline_1m/joeusdt@kline_1m/achusdt@kline_1m/gftusdt@kline_1m/cotiusdt@kline_1m/idexusdt@kline_1m/aliceusdt@kline_1m/superusdt@kline_1m/suiusdt@kline_1m/pythusdt@kline_1m/wldusdt@kline_1m
[2025-07-25T05:40:38.674Z] [INFO] Connected to Binance Futures WebSocket for connection 4 with 25 pairs
[2025-07-25T05:40:38.680Z] [INFO] WebSocket connection 3 opened: wss://fstream.binance.com/ws/dashusdt@kline_1m/zecusdt@kline_1m/batusdt@kline_1m/crvusdt@kline_1m/scusdt@kline_1m/neousdt@kline_1m/egldusdt@kline_1m/sfpusdt@kline_1m/anclusdt@kline_1m/oneusdt@kline_1m/lrcusdt@kline_1m/runeusdt@kline_1m/mkrusdt@kline_1m/lunausdt@kline_1m/ksmusdt@kline_1m/xemusdt@kline_1m/iotausdt@kline_1m/xtzusdt@kline_1m/rvnusdt@kline_1m/kavausdt@kline_1m/ctkusdt@kline_1m/arusdt@kline_1m/zenusdt@kline_1m/sxpusdt@kline_1m/sklusdt@kline_1m
[2025-07-25T05:40:38.681Z] [INFO] Connected to Binance Futures WebSocket for connection 3 with 25 pairs
