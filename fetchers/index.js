const TickCollector = require('./tick-collector');
const TickWorker = require('./tick-worker');
const HealthServer = require('./health-server');
const logger = require('./logger');

class BinanceTickCollectorSystem {
  constructor() {
    this.tickCollector = null;
    this.tickWorker = null;
    this.healthServer = null;
  }

  async start() {
    logger.info('Starting Binance Tick Collector System...');
    
    try {
      // Initialize and start tick collector
      this.tickCollector = new TickCollector();
      this.tickCollector.start();
      
      // Initialize and start tick workers
      this.tickWorker = new TickWorker();
      this.tickWorker.start();
      
      // Initialize and start health server
      this.healthServer = new HealthServer(3000);
      this.healthServer.setTickCollector(this.tickCollector);
      this.healthServer.setTickWorker(this.tickWorker);
      this.healthServer.start();
      
      logger.info('Binance Tick Collector System started successfully');
      
      // Log system info
      this.logSystemInfo();
    } catch (error) {
      logger.error(`Failed to start system: ${error.message}`);
      process.exit(1);
    }
  }

  logSystemInfo() {
    const os = require('os');
    const cpuCount = os.cpus().length;
    const totalMemory = Math.round(os.totalmem() / (1024 * 1024 * 1024));
    
    logger.info('=== System Information ===');
    logger.info(`CPU Cores: ${cpuCount}`);
    logger.info(`Total Memory: ${totalMemory} GB`);
    logger.info(`Platform: ${os.platform()} ${os.arch()}`);
    logger.info(`Node.js Version: ${process.version}`);
    logger.info('========================');
  }

  async shutdown() {
    logger.info('Shutting down Binance Tick Collector System...');
    
    try {
      // Close health server
      if (this.healthServer) {
        this.healthServer.close();
      }
      
      // Close tick collector
      if (this.tickCollector) {
        this.tickCollector.close();
      }
      
      // Close tick workers
      if (this.tickWorker) {
        await this.tickWorker.close();
      }
      
      logger.info('Binance Tick Collector System shut down successfully');
      process.exit(0);
    } catch (error) {
      logger.error(`Error during shutdown: ${error.message}`);
      process.exit(1);
    }
  }
}

// Create and start the system
const system = new BinanceTickCollectorSystem();

// Handle graceful shutdown
const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];
signals.forEach(signal => {
  process.on(signal, async () => {
    logger.info(`Received ${signal}, shutting down gracefully...`);
    await system.shutdown();
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error(`Uncaught Exception: ${error.message}`);
  logger.error(error.stack);
  system.shutdown();
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  system.shutdown();
});

// Start the system
system.start();

module.exports = BinanceTickCollectorSystem;
