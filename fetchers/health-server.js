const express = require('express');
const { connection } = require('./queue');
const logger = require('./logger');

class HealthServer {
  constructor(port = 3000) {
    this.port = port;
    this.app = express();
    this.server = null;
    this.tickCollector = null;
    this.tickWorker = null;
    
    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    this.app.use(express.json());
    
    // Add CORS headers
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
      next();
    });
  }

  setupRoutes() {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      });
    });

    // Status endpoint
    this.app.get('/status', async (req, res) => {
      try {
        const status = {
          timestamp: new Date().toISOString(),
          system: {
            redis: await this.getRedisStatus(),
            tickCollector: this.tickCollector ? this.tickCollector.getStatus() : null,
            tickWorker: this.tickWorker ? this.tickWorker.getStatus() : null
          }
        };
        
        res.status(200).json(status);
      } catch (error) {
        logger.error(`Error getting status: ${error.message}`);
        res.status(500).json({
          error: 'Failed to get status',
          message: error.message
        });
      }
    });

    // Metrics endpoint (Prometheus compatible)
    this.app.get('/metrics', async (req, res) => {
      try {
        const metrics = await this.getMetrics();
        res.set('Content-Type', 'text/plain');
        res.status(200).send(metrics);
      } catch (error) {
        logger.error(`Error getting metrics: ${error.message}`);
        res.status(500).json({
          error: 'Failed to get metrics',
          message: error.message
        });
      }
    });

    // Root endpoint
    this.app.get('/', (req, res) => {
      res.status(200).json({
        message: 'Binance Tick Collector Health Server',
        endpoints: {
          health: '/health',
          status: '/status',
          metrics: '/metrics'
        }
      });
    });
  }

  async getRedisStatus() {
    try {
      const info = await connection.info();
      const time = await connection.time();
      
      return {
        connected: connection.status === 'ready',
        info: info,
        serverTime: time
      };
    } catch (error) {
      return {
        connected: false,
        error: error.message
      };
    }
  }

  async getMetrics() {
    try {
      // Get queue metrics
      const queue = require('./queue').tickQueue;
      const counts = await queue.getJobCounts();
      
      // Get Redis info
      const redisInfo = await connection.info();
      
      // Format metrics in Prometheus format
      let metrics = '';
      
      // Queue metrics
      metrics += '# HELP tick_queue_jobs Number of jobs in tick queue\n';
      metrics += '# TYPE tick_queue_jobs gauge\n';
      metrics += `tick_queue_jobs{state="waiting"} ${counts.waiting || 0}\n`;
      metrics += `tick_queue_jobs{state="active"} ${counts.active || 0}\n`;
      metrics += `tick_queue_jobs{state="completed"} ${counts.completed || 0}\n`;
      metrics += `tick_queue_jobs{state="failed"} ${counts.failed || 0}\n`;
      metrics += `tick_queue_jobs{state="delayed"} ${counts.delayed || 0}\n`;
      
      // System metrics
      metrics += '# HELP process_uptime_seconds Process uptime in seconds\n';
      metrics += '# TYPE process_uptime_seconds gauge\n';
      metrics += `process_uptime_seconds ${process.uptime()}\n`;
      
      // Memory metrics
      const memoryUsage = process.memoryUsage();
      metrics += '# HELP process_memory_bytes Process memory usage in bytes\n';
      metrics += '# TYPE process_memory_bytes gauge\n';
      metrics += `process_memory_bytes{type="heap_used"} ${memoryUsage.heapUsed}\n`;
      metrics += `process_memory_bytes{type="heap_total"} ${memoryUsage.heapTotal}\n`;
      metrics += `process_memory_bytes{type="rss"} ${memoryUsage.rss}\n`;
      metrics += `process_memory_bytes{type="external"} ${memoryUsage.external}\n`;
      
      return metrics;
    } catch (error) {
      logger.error(`Error getting metrics: ${error.message}`);
      throw error;
    }
  }

  setTickCollector(tickCollector) {
    this.tickCollector = tickCollector;
  }

  setTickWorker(tickWorker) {
    this.tickWorker = tickWorker;
  }

  start() {
    this.server = this.app.listen(this.port, () => {
      logger.info(`Health server listening on port ${this.port}`);
    });
    
    this.server.on('error', (error) => {
      logger.error(`Health server error: ${error.message}`);
    });
  }

  close() {
    if (this.server) {
      this.server.close(() => {
        logger.info('Health server closed');
      });
    }
  }
}

module.exports = HealthServer;
