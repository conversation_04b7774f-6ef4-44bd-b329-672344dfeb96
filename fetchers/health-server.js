const express = require('express');
const { createServer } = require('http');
const { Server } = require('socket.io');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const compression = require('compression');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { connection, tickQueue } = require('./queue');
const logger = require('./logger');

class HealthServer {
  constructor(port = 3000) {
    this.port = port;
    this.app = express();
    this.server = null;
    this.io = null;
    this.tickCollector = null;
    this.tickWorker = null;
    this.pairStates = new Map();
    this.systemMetrics = {
      startTime: Date.now(),
      totalProcessed: 0,
      lastUpdate: Date.now()
    };

    this.setupMiddleware();
    this.setupRoutes();
    this.setupWebSocket();
    this.startMetricsCollection();
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: false // Allow inline scripts for dashboard
    }));

    // Compression
    this.app.use(compression());

    // CORS
    this.app.use(cors({
      origin: "*",
      methods: ["GET", "POST", "PUT", "DELETE"],
      allowedHeaders: ["Content-Type", "Authorization"]
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 1000 // limit each IP to 1000 requests per windowMs
    });
    this.app.use('/api/', limiter);

    this.app.use(express.json());
    this.app.use(express.static(path.join(__dirname, 'public')));
  }

  setupRoutes() {
    // Serve dashboard
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, 'public', 'dashboard.html'));
    });

    // API Routes
    // Health check endpoint
    this.app.get('/api/health', (req, res) => {
      res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      });
    });

    // Enhanced status endpoint
    this.app.get('/api/status', async (req, res) => {
      try {
        const status = await this.getDetailedStatus();
        res.status(200).json(status);
      } catch (error) {
        logger.error(`Error getting status: ${error.message}`);
        res.status(500).json({
          error: 'Failed to get status',
          message: error.message
        });
      }
    });

    // Pair-specific status
    this.app.get('/api/pairs', async (req, res) => {
      try {
        const pairStatus = await this.getPairStatus();
        res.status(200).json(pairStatus);
      } catch (error) {
        logger.error(`Error getting pair status: ${error.message}`);
        res.status(500).json({
          error: 'Failed to get pair status',
          message: error.message
        });
      }
    });

    // System metrics
    this.app.get('/api/metrics', async (req, res) => {
      try {
        const metrics = await this.getSystemMetrics();
        res.status(200).json(metrics);
      } catch (error) {
        logger.error(`Error getting metrics: ${error.message}`);
        res.status(500).json({
          error: 'Failed to get metrics',
          message: error.message
        });
      }
    });

    // Pair control endpoints
    this.app.post('/api/pairs/:pair/start', async (req, res) => {
      try {
        const { pair } = req.params;
        await this.startPair(pair);
        res.status(200).json({ success: true, message: `Started ${pair}` });
      } catch (error) {
        logger.error(`Error starting pair ${req.params.pair}: ${error.message}`);
        res.status(500).json({
          error: 'Failed to start pair',
          message: error.message
        });
      }
    });

    this.app.post('/api/pairs/:pair/stop', async (req, res) => {
      try {
        const { pair } = req.params;
        await this.stopPair(pair);
        res.status(200).json({ success: true, message: `Stopped ${pair}` });
      } catch (error) {
        logger.error(`Error stopping pair ${req.params.pair}: ${error.message}`);
        res.status(500).json({
          error: 'Failed to stop pair',
          message: error.message
        });
      }
    });

    // Queue management
    this.app.get('/api/queue/stats', async (req, res) => {
      try {
        const stats = await this.getQueueStats();
        res.status(200).json(stats);
      } catch (error) {
        logger.error(`Error getting queue stats: ${error.message}`);
        res.status(500).json({
          error: 'Failed to get queue stats',
          message: error.message
        });
      }
    });

    // Storage info
    this.app.get('/api/storage', async (req, res) => {
      try {
        const storage = await this.getStorageInfo();
        res.status(200).json(storage);
      } catch (error) {
        logger.error(`Error getting storage info: ${error.message}`);
        res.status(500).json({
          error: 'Failed to get storage info',
          message: error.message
        });
      }
    });

    // Prometheus metrics endpoint
    this.app.get('/metrics', async (req, res) => {
      try {
        const metrics = await this.getPrometheusMetrics();
        res.set('Content-Type', 'text/plain');
        res.status(200).send(metrics);
      } catch (error) {
        logger.error(`Error getting metrics: ${error.message}`);
        res.status(500).json({
          error: 'Failed to get metrics',
          message: error.message
        });
      }
    });
  }

  setupWebSocket() {
    this.server = require('http').createServer(this.app);
    this.io = require('socket.io')(this.server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });

    this.io.on('connection', (socket) => {
      logger.info(`Client connected: ${socket.id}`);

      // Send initial data
      this.sendInitialData(socket);

      socket.on('disconnect', () => {
        logger.info(`Client disconnected: ${socket.id}`);
      });

      socket.on('request-update', async () => {
        await this.sendRealTimeUpdate(socket);
      });
    });
  }

  startMetricsCollection() {
    // Update metrics every 5 seconds
    setInterval(async () => {
      try {
        await this.updateMetrics();
        this.broadcastMetrics();
      } catch (error) {
        logger.error(`Error updating metrics: ${error.message}`);
      }
    }, 5000);
  }

  async sendInitialData(socket) {
    try {
      const data = {
        status: await this.getDetailedStatus(),
        pairs: await this.getPairStatus(),
        metrics: await this.getSystemMetrics(),
        storage: await this.getStorageInfo()
      };
      socket.emit('initial-data', data);
    } catch (error) {
      logger.error(`Error sending initial data: ${error.message}`);
    }
  }

  async sendRealTimeUpdate(socket) {
    try {
      const data = {
        timestamp: Date.now(),
        metrics: await this.getSystemMetrics(),
        pairs: await this.getPairStatus(),
        queue: await this.getQueueStats()
      };
      socket.emit('real-time-update', data);
    } catch (error) {
      logger.error(`Error sending real-time update: ${error.message}`);
    }
  }

  broadcastMetrics() {
    this.io.emit('metrics-update', {
      timestamp: Date.now(),
      systemMetrics: this.systemMetrics
    });
  }

  async updateMetrics() {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    this.systemMetrics = {
      ...this.systemMetrics,
      lastUpdate: Date.now(),
      memory: {
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        rss: memUsage.rss,
        external: memUsage.external,
        heapUsedPercent: (memUsage.heapUsed / memUsage.heapTotal) * 100
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      uptime: process.uptime()
    };
  }

  async getRedisStatus() {
    try {
      const info = await connection.info();
      const time = await connection.time();

      return {
        connected: connection.status === 'ready',
        info: info,
        serverTime: time
      };
    } catch (error) {
      return {
        connected: false,
        error: error.message
      };
    }
  }

  async getDetailedStatus() {
    const status = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      system: {
        redis: await this.getRedisStatus(),
        tickCollector: this.tickCollector ? this.tickCollector.getStatus() : null,
        tickWorker: this.tickWorker ? this.tickWorker.getStatus() : null
      },
      health: 'healthy'
    };

    // Determine overall health
    if (!status.system.redis.connected) {
      status.health = 'unhealthy';
    }

    return status;
  }

  async getPairStatus() {
    try {
      const symbolsConfig = require('./symbols.json');
      const pairStatus = [];

      // Get all pairs from config
      const allPairs = [];
      symbolsConfig.connections.forEach(connection => {
        connection.pairs.forEach(pair => {
          allPairs.push(pair);
        });
      });

      // Get queue stats for each pair
      for (const pair of allPairs) {
        const pairData = this.pairStates.get(pair) || {
          lastUpdate: null,
          recordCount: 0,
          status: 'unknown',
          errors: 0
        };

        pairStatus.push({
          pair,
          ...pairData,
          timeSinceLastUpdate: pairData.lastUpdate ? Date.now() - pairData.lastUpdate : null
        });
      }

      return pairStatus;
    } catch (error) {
      logger.error(`Error getting pair status: ${error.message}`);
      return [];
    }
  }

  async getSystemMetrics() {
    const memUsage = process.memoryUsage();
    const totalMem = require('os').totalmem();

    return {
      timestamp: Date.now(),
      uptime: process.uptime(),
      memory: {
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        rss: memUsage.rss,
        external: memUsage.external,
        heapUsedPercent: (memUsage.heapUsed / memUsage.heapTotal) * 100,
        systemUsedPercent: (memUsage.rss / totalMem) * 100
      },
      cpu: {
        loadAverage: require('os').loadavg(),
        cpuCount: require('os').cpus().length
      }
    };
  }

  async getQueueStats() {
    try {
      const queue = require('./queue').tickQueue;
      const counts = await queue.getJobCounts();

      return {
        waiting: counts.waiting || 0,
        active: counts.active || 0,
        completed: counts.completed || 0,
        failed: counts.failed || 0,
        delayed: counts.delayed || 0,
        total: (counts.waiting || 0) + (counts.active || 0) + (counts.delayed || 0)
      };
    } catch (error) {
      logger.error(`Error getting queue stats: ${error.message}`);
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0,
        total: 0
      };
    }
  }

  async getStorageInfo() {
    try {
      const fs = require('fs');
      const path = require('path');
      const dataDir = path.join(__dirname, 'data');

      if (!fs.existsSync(dataDir)) {
        return {
          totalSize: 0,
          fileCount: 0,
          files: []
        };
      }

      const files = fs.readdirSync(dataDir);
      let totalSize = 0;
      const fileDetails = [];

      for (const file of files) {
        if (file.endsWith('.sqlite')) {
          const filePath = path.join(dataDir, file);
          const stats = fs.statSync(filePath);
          totalSize += stats.size;
          fileDetails.push({
            name: file,
            size: stats.size,
            created: stats.birthtime,
            modified: stats.mtime
          });
        }
      }

      return {
        totalSize,
        totalSizeMB: (totalSize / (1024 * 1024)).toFixed(2),
        fileCount: fileDetails.length,
        files: fileDetails.sort((a, b) => b.modified - a.modified)
      };
    } catch (error) {
      logger.error(`Error getting storage info: ${error.message}`);
      return {
        totalSize: 0,
        fileCount: 0,
        files: []
      };
    }
  }

  async startPair(pair) {
    // Implementation for starting a specific pair
    logger.info(`Starting pair: ${pair}`);
    this.pairStates.set(pair, {
      ...this.pairStates.get(pair),
      status: 'active',
      lastUpdate: Date.now()
    });
  }

  async stopPair(pair) {
    // Implementation for stopping a specific pair
    logger.info(`Stopping pair: ${pair}`);
    this.pairStates.set(pair, {
      ...this.pairStates.get(pair),
      status: 'stopped',
      lastUpdate: Date.now()
    });
  }

  async getPrometheusMetrics() {
    try {
      // Get queue metrics
      const queue = require('./queue').tickQueue;
      const counts = await queue.getJobCounts();

      // Format metrics in Prometheus format
      let metrics = '';

      // Queue metrics
      metrics += '# HELP tick_queue_jobs Number of jobs in tick queue\n';
      metrics += '# TYPE tick_queue_jobs gauge\n';
      metrics += `tick_queue_jobs{state="waiting"} ${counts.waiting || 0}\n`;
      metrics += `tick_queue_jobs{state="active"} ${counts.active || 0}\n`;
      metrics += `tick_queue_jobs{state="completed"} ${counts.completed || 0}\n`;
      metrics += `tick_queue_jobs{state="failed"} ${counts.failed || 0}\n`;
      metrics += `tick_queue_jobs{state="delayed"} ${counts.delayed || 0}\n`;

      // System metrics
      metrics += '# HELP process_uptime_seconds Process uptime in seconds\n';
      metrics += '# TYPE process_uptime_seconds gauge\n';
      metrics += `process_uptime_seconds ${process.uptime()}\n`;

      // Memory metrics
      const memoryUsage = process.memoryUsage();
      metrics += '# HELP process_memory_bytes Process memory usage in bytes\n';
      metrics += '# TYPE process_memory_bytes gauge\n';
      metrics += `process_memory_bytes{type="heap_used"} ${memoryUsage.heapUsed}\n`;
      metrics += `process_memory_bytes{type="heap_total"} ${memoryUsage.heapTotal}\n`;
      metrics += `process_memory_bytes{type="rss"} ${memoryUsage.rss}\n`;
      metrics += `process_memory_bytes{type="external"} ${memoryUsage.external}\n`;

      return metrics;
    } catch (error) {
      logger.error(`Error getting metrics: ${error.message}`);
      throw error;
    }
  }

  setTickCollector(tickCollector) {
    this.tickCollector = tickCollector;
  }

  setTickWorker(tickWorker) {
    this.tickWorker = tickWorker;
  }

  updatePairState(pair, data) {
    const currentState = this.pairStates.get(pair) || {
      recordCount: 0,
      errors: 0,
      status: 'unknown'
    };

    this.pairStates.set(pair, {
      ...currentState,
      ...data,
      lastUpdate: Date.now()
    });
  }

  start() {
    if (!this.server) {
      this.setupWebSocket();
    }

    this.server.listen(this.port, () => {
      logger.info(`Health server with dashboard listening on port ${this.port}`);
      logger.info(`Dashboard available at: http://localhost:${this.port}`);
    });

    this.server.on('error', (error) => {
      logger.error(`Health server error: ${error.message}`);
    });
  }

  close() {
    if (this.server) {
      this.server.close(() => {
        logger.info('Health server closed');
      });
    }
    if (this.io) {
      this.io.close();
    }
  }
}

module.exports = HealthServer;
