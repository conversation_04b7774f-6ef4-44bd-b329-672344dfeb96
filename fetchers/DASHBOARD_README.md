# 🚀 Binance Tick Collector - Modern Monitoring Dashboard

## 📊 Özellikler

### 🎯 Ana Dashboard Özellikleri
- **Real-time Monitoring**: Canlı sistem durumu ve performans metrikleri
- **Trading Pair Yönetimi**: 116 trading pair'in durumunu izleme ve kontrol etme
- **Sistem Performansı**: CPU, RAM kullanımı ve queue durumu
- **Storage Monitoring**: SQLite veritabanı boyutu ve dosya detayları
- **Activity Log**: Real-time sistem aktivite günlükleri
- **Problem Detection**: Sorunlu pair'leri otomatik tespit etme

### 🔧 Teknik Özellikler
- **Modern UI**: Tailwind CSS ile responsive tasarım
- **Real-time Updates**: Socket.IO ile canlı veri akışı
- **Interactive Charts**: Chart.js ile dinamik grafikler
- **RESTful API**: Comprehensive monitoring endpoints
- **Security**: Helmet.js, CORS, rate limiting
- **Performance**: Compression, caching optimizations

## 🎨 Dashboard Bileşenleri

### 1. System Overview Cards
- **System Status**: Genel sistem durumu (Healthy/Unhealthy)
- **Memory Usage**: RAM kullanım yüzdesi
- **Queue Size**: Bekleyen işlem sayısı
- **Storage Used**: Toplam kullanılan disk alanı

### 2. Performance Charts
- **Memory Usage Over Time**: Zaman içinde bellek kullanımı
- **Queue Statistics**: Queue durumu (waiting, active, failed)

### 3. Trading Pairs Management
- **Pair Status Grid**: Tüm trading pair'lerin durumu
- **Filter & Search**: Pair'leri filtreleme ve arama
- **Individual Controls**: Her pair için start/stop butonları
- **Bulk Operations**: Tüm pair'leri toplu start/stop
- **Status Indicators**: 
  - 🟢 Active: Aktif veri akışı
  - 🟡 Warning: Gecikmeli veri
  - 🔴 Error: Hata durumu
  - ⚫ Stopped: Durdurulmuş

### 4. Problem Pairs Section
- **Automatic Detection**: Sorunlu pair'leri otomatik tespit
- **Error Tracking**: Hata sayısı ve türü
- **Last Update Tracking**: Son veri alma zamanı
- **Quick Actions**: Hızlı müdahale butonları

### 5. System Details
- **Redis Status**: Redis bağlantı durumu ve bilgileri
- **Storage Details**: Dosya listesi ve boyutları
- **Performance Metrics**: CPU load, memory, queue load progress bars

### 6. Activity Log
- **Real-time Logging**: Sistem aktivitelerinin canlı takibi
- **Color Coded**: Farklı log seviyeleri için renk kodları
- **Pause/Resume**: Log akışını durdurma/devam ettirme
- **Clear Function**: Log geçmişini temizleme
- **Auto-scroll**: Yeni log'lar için otomatik kaydırma

## 🌐 API Endpoints

### Monitoring Endpoints
```
GET  /api/health          - Sistem sağlık durumu
GET  /api/status          - Detaylı sistem durumu
GET  /api/pairs           - Trading pair durumları
GET  /api/metrics         - Sistem metrikleri
GET  /api/queue/stats     - Queue istatistikleri
GET  /api/storage         - Storage bilgileri
GET  /metrics             - Prometheus metrikleri
```

### Control Endpoints
```
POST /api/pairs/:pair/start  - Pair'i başlat
POST /api/pairs/:pair/stop   - Pair'i durdur
```

## 🚀 Kullanım

### Dashboard'a Erişim
```bash
# Sistem başlatma
npm start

# Dashboard URL
http://localhost:3000
```

### Pair Yönetimi
1. **Individual Control**: Her pair'in yanındaki ▶️ (start) ve ⏹️ (stop) butonları
2. **Bulk Operations**: "Start All" ve "Stop All" butonları
3. **Filtering**: Pair adı veya duruma göre filtreleme
4. **Search**: Üst kısımdaki arama kutusu

### Monitoring
1. **Real-time Updates**: Dashboard otomatik olarak 5 saniyede bir güncellenir
2. **Manual Refresh**: "Refresh" butonu ile manuel güncelleme
3. **Connection Status**: Header'da bağlantı durumu göstergesi

### Problem Solving
1. **Problem Pairs**: Sağ panelde sorunlu pair'ler otomatik listelenir
2. **Activity Log**: Alt kısımda tüm sistem aktiviteleri görüntülenir
3. **Performance Metrics**: CPU, RAM ve queue yükü progress bar'larında

## 📱 Responsive Design

Dashboard tüm cihazlarda optimize edilmiştir:
- **Desktop**: Full feature set
- **Tablet**: Responsive grid layout
- **Mobile**: Stacked layout, touch-friendly controls

## 🎨 UI/UX Features

### Visual Indicators
- **Status Colors**: Green (healthy), Yellow (warning), Red (error), Gray (unknown)
- **Glow Effects**: Critical status'ler için glow efektleri
- **Animations**: Smooth transitions ve hover effects
- **Progress Bars**: Visual performance indicators

### Interactive Elements
- **Hover Effects**: Card'lar ve butonlar için hover animasyonları
- **Click Feedback**: Button press animasyonları
- **Loading States**: İşlem sırasında loading göstergeleri
- **Notifications**: Toast notifications for actions

### Accessibility
- **Color Contrast**: WCAG uyumlu renk kontrastları
- **Font Sizes**: Okunabilir font boyutları
- **Touch Targets**: Mobile-friendly touch areas
- **Keyboard Navigation**: Keyboard accessibility

## 🔧 Customization

### Styling
- `public/styles.css`: Custom CSS styles
- Tailwind classes: Utility-first styling
- Chart.js themes: Customizable chart appearances

### Configuration
- Update intervals: Modify in `dashboard.js`
- Chart data points: Adjust `maxDataPoints`
- Log entries: Configure `maxLogEntries`

## 📊 Monitoring Metrics

### System Metrics
- **CPU Usage**: Load average based percentage
- **Memory Usage**: Heap and system memory
- **Queue Load**: Active jobs in Redis queue
- **Storage Usage**: SQLite database sizes

### Pair Metrics
- **Record Count**: Total processed records per pair
- **Last Update**: Time since last data received
- **Error Count**: Number of errors per pair
- **Status**: Current operational status

### Performance Tracking
- **Throughput**: Records processed per second
- **Latency**: Data processing delays
- **Error Rate**: Error percentage over time
- **Uptime**: System availability metrics

## 🛠️ Troubleshooting

### Common Issues
1. **Dashboard Not Loading**: Check if port 3000 is available
2. **No Data Updates**: Verify WebSocket connection
3. **Pair Controls Not Working**: Check API endpoint accessibility
4. **Charts Not Displaying**: Verify Chart.js library loading

### Debug Mode
Enable debug logging in `.env`:
```
DEBUG_MODE=true
```

## 🔮 Future Enhancements

### Planned Features
- **Dark Mode**: Theme switching capability
- **Export Functions**: Data export to CSV/JSON
- **Alert System**: Email/SMS notifications
- **Historical Data**: Long-term trend analysis
- **Custom Dashboards**: User-configurable layouts
- **Multi-language**: Internationalization support

### Performance Optimizations
- **Data Compression**: Reduce bandwidth usage
- **Caching**: Improve response times
- **Lazy Loading**: Optimize initial load
- **Service Worker**: Offline capability
