
Sen profesyonel bir backend AI agentsin. Görevin: Mac Studio M1 Max üzerinde çalışan, Binance Futures piyasasından 100 işlem çiftinin tick verisini WebSocket üzerinden toplayan, Redis queue sistemi ile <PERSON>, çok çekirdekli (10 core) işleme mimarisiyle SQLite'e kayıt eden, kendi bağlantı durumunu izleyip gerektiğinde otomatik restart eden, gözlemlenebilir ve dayanıklı bir sistem oluşturmaktır.

📦 TEKNOLOJİ YIĞINI:
- Node.js (v18+)
- Redis (ioredis / BullMQ)
- SQLite (better-sqlite3 veya sqlite3 modülü)
- Çok çekirdekli kullanım: `worker_threads` veya `cluster`
- Donanım: Mac Studio M1 Max (10 core, 32 GB RAM)

🎯 GÖREVLER:
1. Sistemi aşağıdaki modüllere böl:
tick-collector.js → Binance WebSocket üzerinden tick verisi toplayıp Redis kuyruğuna aktarır

tick-worker.js → Redis kuyruğundan tick verisi alıp SQLite'e yazar (çok çekirdekli işlem)

queue.js → Redis bağlantısı ve ortak queue tanımı

sqlite-writer.js → SQLite işlemleri ve batch insert fonksiyonu

websocket-monitor.js → WebSocket bağlantı durumu izleme, yeniden başlatma (self-healing)

health-server.js → Express.js ile HTTP health endpoint'i ve bağlantı durumu raporu

logger.js → Loglama (günlük bazlı log dosyaları)


2. 100 sembolü 2 WebSocket bağlantısına böl ve her bağlantıyı `websocket-monitor.js` ile izlet:
- Her bağlantı ayrı bir `tick-streamer` olarak izlenir
- 10 saniyeden fazla veri gelmezse otomatik restart edilir
- Yeniden bağlanma limiti / backoff retry algoritması uygulanır

3. Gelen tick verisi Redis’te `tickQueue` kuyruğuna aktarılır

4. `tick-worker.js` her CPU çekirdeği için 1 worker thread başlatır (`os.cpus().length`)
- Her worker Redis’ten veri çeker
- Her sembol için tablo açar (örnek: `btcusdt`)
- Tick verilerini `ticks-YYYY-MM-DD.sqlite` dosyasına batch olarak yazar
- Flush interval: 100 kayıt / 1 saniye (hangisi önce dolarsa)

5. `.jsonl` dosyası tutulmaz, tüm veri doğrudan SQLite’e gider

6. Sistemin **gözlemlenebilirlik ve uyarı özellikleri**:
- Her WebSocket bağlantısının durumu `health-server.js` üzerinden `/status` endpoint'inden izlenebilir
- Redis kuyruk durumu (`waiting`, `active`, `failed`, `completed`) loglanır
- Günlük `logs/YYYY-MM-DD.log` dosyasında tüm hatalar ve reconnect eventleri kayıt altına alınır
- Prometheus uyumlu `/metrics` endpoint'i eklenebilir (opsiyonel)

7. Her gün sonunda SQLite dosyası kapatılır, salt okunur yapılabilir veya S3 gibi bir storage'a gönderilebilir (opsiyonel)

8. Sistem başlatıldığında:
- Redis bağlantısı kurulur
- Her WebSocket bağlantısı başlatılır ve monitor edilir
- Tüm CPU çekirdekleri için worker başlatılır
- Health server devreye girer

🛠 DONANIM GEREKLİLİKLERİ:
- Donanım: Mac Studio M1 Max (10 core CPU, 32 GB RAM)
- SSD disk önerilir (min 256 GB) — günlük yaklaşık 250-500 MB veri yazımı olur
- Redis çalışıyor olmalı (`localhost:6379`)
- Node.js v18+, `pm2` ile yönetilebilir olmalı

💬 AJANIN GÖREVİ:
Aşağıdaki her bileşeni kodla ve çalışır hale getir:
- tick-collector.js
- websocket-monitor.js
- tick-worker.js
- queue.js
- sqlite-writer.js
- health-server.js
- logger.js
- symbols.json (örnek 100 pair listesi)
- package.json ve README.md

Kodlar test edilebilir, extensible ve production-grade olmalı. Hataları kaldır, reconnect ve ölçeklenebilirliği yönet. Yazılım çalıştırıldığında veri toplamaya başlasın ve kendi kendine izleyerek stabil şekilde sürsün.