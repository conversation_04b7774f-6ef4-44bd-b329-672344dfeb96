const { Queue, Worker, QueueEvents } = require('bullmq');
const IORedis = require('ioredis');
const logger = require('./logger');
const dotenv = require('dotenv');
// Load environment variables from .env file
dotenv.config();

const debugMode = process.env.DEBUG_MODE === 'true';

// Redis connection configuration
const redisConfig = {
  host: 'localhost',
  port: 6379,
  maxRetriesPerRequest: null,
  enableReadyCheck: false
};

// Create Redis connection instances
const connection = new IORedis(redisConfig);

// Handle Redis connection events
connection.on('connect', () => {
  debugMode && logger.info('Redis connection established');
});

connection.on('ready', () => {
  logger.info('Redis connection ready');
});

connection.on('error', (err) => {
  debugMode && logger.error(`Redis connection error: ${err.message}`);
});

connection.on('close', () => {
  debugMode && logger.warn('Redis connection closed');
});

connection.on('reconnecting', () => {
  logger.info('Redis reconnecting...');
});

// Create the tick queue
const tickQueue = new Queue('tickQueue', {
  connection,
  defaultJobOptions: {
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 1000
    },
    removeOnComplete: true,
    removeOnFail: false
  }
});

// Queue events for monitoring
const queueEvents = new QueueEvents('tickQueue', { connection });

queueEvents.on('waiting', ({ jobId }) => {
  debugMode && logger.debug(`Job ${jobId} is waiting`);
});

queueEvents.on('active', ({ jobId, prev }) => {
  debugMode && logger.debug(`Job ${jobId} is now active (prev: ${prev})`);
});

queueEvents.on('completed', ({ jobId, returnvalue }) => {
  debugMode && logger.debug(`Job ${jobId} completed with return value: ${returnvalue}`);
});

queueEvents.on('failed', ({ jobId, failedReason }) => {
  logger.error(`Job ${jobId} failed with reason: ${failedReason}`);
});

queueEvents.on('error', (err) => {
  logger.error(`Queue error: ${err.message}`);
});

module.exports = {
  tickQueue,
  Worker,
  connection,
  queueEvents
};
