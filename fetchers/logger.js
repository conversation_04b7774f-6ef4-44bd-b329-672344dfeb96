const fs = require('fs');
const path = require('path');

class Logger {
  constructor() {
    this.logDir = path.join(__dirname, 'logs');
    this.ensureLogDirectory();
    this.currentDate = this.getCurrentDate();
    this.logFile = this.getLogFilePath();
  }

  ensureLogDirectory() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  getCurrentDate() {
    const now = new Date();
    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
  }

  getLogFilePath() {
    return path.join(this.logDir, `${this.currentDate}.log`);
  }

  checkDateRoll() {
    const currentDate = this.getCurrentDate();
    if (currentDate !== this.currentDate) {
      this.currentDate = currentDate;
      this.logFile = this.getLogFilePath();
    }
  }

  writeLog(level, message) {
    this.checkDateRoll();
    
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level}] ${message}\n`;
    
    fs.appendFileSync(this.logFile, logMessage);
    console.log(logMessage.trim());
  }

  info(message) {
    this.writeLog('INFO', message);
  }

  warn(message) {
    this.writeLog('WARN', message);
  }

  error(message) {
    this.writeLog('ERROR', message);
  }

  debug(message) {
    this.writeLog('DEBUG', message);
  }
}

module.exports = new Logger();
