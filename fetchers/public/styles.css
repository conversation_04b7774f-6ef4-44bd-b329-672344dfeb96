/* Custom animations and styles for the dashboard */

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
    40%, 43% { transform: translate3d(0, -30px, 0); }
    70% { transform: translate3d(0, -15px, 0); }
    90% { transform: translate3d(0, -4px, 0); }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

.pulse-slow {
    animation: pulse 3s ease-in-out infinite;
}

.bounce {
    animation: bounce 2s infinite;
}

/* Custom scrollbar */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Gradient backgrounds */
.gradient-blue {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-green {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.gradient-purple {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-orange {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* Card hover effects */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Status indicators with glow effect */
.status-indicator-glow {
    box-shadow: 0 0 10px currentColor;
}

.status-healthy-glow {
    color: #10b981;
    box-shadow: 0 0 10px #10b981;
}

.status-warning-glow {
    color: #f59e0b;
    box-shadow: 0 0 10px #f59e0b;
}

.status-error-glow {
    color: #ef4444;
    box-shadow: 0 0 10px #ef4444;
}

/* Loading spinner */
.spinner {
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notification styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 16px 24px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background-color: #10b981;
}

.notification.error {
    background-color: #ef4444;
}

.notification.warning {
    background-color: #f59e0b;
}

.notification.info {
    background-color: #3b82f6;
}

/* Chart container */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Responsive grid */
@media (max-width: 768px) {
    .grid-responsive {
        grid-template-columns: 1fr;
    }
}

@media (min-width: 768px) {
    .grid-responsive {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .grid-responsive {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Pair status cards */
.pair-status-card {
    border-left: 4px solid transparent;
    transition: all 0.3s ease;
}

.pair-status-card.active {
    border-left-color: #10b981;
    background-color: #f0fdf4;
}

.pair-status-card.error {
    border-left-color: #ef4444;
    background-color: #fef2f2;
}

.pair-status-card.warning {
    border-left-color: #f59e0b;
    background-color: #fffbeb;
}

.pair-status-card.stopped {
    border-left-color: #6b7280;
    background-color: #f9fafb;
}

/* Button animations */
.btn-animate {
    transition: all 0.2s ease;
}

.btn-animate:hover {
    transform: scale(1.05);
}

.btn-animate:active {
    transform: scale(0.95);
}

/* Progress bars */
.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transition: width 0.3s ease;
}

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: #1f2937;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .dark-mode {
        background-color: #1f2937;
        color: #f9fafb;
    }
    
    .dark-mode .card {
        background-color: #374151;
        border-color: #4b5563;
    }
    
    .dark-mode .text-gray-900 {
        color: #f9fafb !important;
    }
    
    .dark-mode .text-gray-600 {
        color: #d1d5db !important;
    }
    
    .dark-mode .bg-gray-50 {
        background-color: #374151 !important;
    }
}

/* Mobile optimizations */
@media (max-width: 640px) {
    .mobile-stack {
        flex-direction: column;
    }
    
    .mobile-full {
        width: 100%;
    }
    
    .mobile-text-sm {
        font-size: 0.875rem;
    }
    
    .mobile-p-2 {
        padding: 0.5rem;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-break {
        page-break-after: always;
    }
}
