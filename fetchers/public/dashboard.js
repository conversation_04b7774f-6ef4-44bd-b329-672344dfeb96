class DashboardManager {
    constructor() {
        this.socket = null;
        this.memoryChart = null;
        this.queueChart = null;
        this.memoryData = [];
        this.queueData = [];
        this.maxDataPoints = 20;
        this.pairs = [];
        this.filteredPairs = [];
        this.activityLog = [];
        this.logPaused = false;
        this.maxLogEntries = 100;

        this.init();
    }

    init() {
        this.connectSocket();
        this.initCharts();
        this.setupEventListeners();
        this.hideLoading();
    }

    connectSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            this.updateConnectionStatus('connected');
            this.addLogEntry('Connected to monitoring server', 'success');
            console.log('Connected to server');
        });

        this.socket.on('disconnect', () => {
            this.updateConnectionStatus('disconnected');
            this.addLogEntry('Disconnected from monitoring server', 'error');
            console.log('Disconnected from server');
        });

        this.socket.on('initial-data', (data) => {
            this.handleInitialData(data);
        });

        this.socket.on('real-time-update', (data) => {
            this.handleRealTimeUpdate(data);
        });

        this.socket.on('metrics-update', (data) => {
            this.updateCharts(data);
        });
    }

    updateConnectionStatus(status) {
        const statusElement = document.getElementById('connection-status');
        const indicator = statusElement.querySelector('.status-indicator');
        
        if (status === 'connected') {
            statusElement.innerHTML = '<span class="status-indicator status-healthy"></span>Connected';
            statusElement.className = 'ml-4 px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800';
        } else {
            statusElement.innerHTML = '<span class="status-indicator status-error"></span>Disconnected';
            statusElement.className = 'ml-4 px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800';
        }
    }

    handleInitialData(data) {
        console.log('Initial data received:', data);
        this.addLogEntry('Dashboard initialized with system data', 'success');
        this.updateSystemOverview(data.status, data.metrics);
        this.updatePairs(data.pairs);
        this.updateRedisStatus(data.status.system.redis);
        this.updateStorageDetails(data.storage);
        this.updateLastUpdate();

        if (data.pairs) {
            this.addLogEntry(`Monitoring ${data.pairs.length} trading pairs`, 'info');
        }
    }

    handleRealTimeUpdate(data) {
        console.log('Real-time update:', data);
        this.updateSystemMetrics(data.metrics);
        this.updatePairs(data.pairs);
        this.updateQueueStats(data.queue);
        this.updateLastUpdate();
    }

    updateSystemOverview(status, metrics) {
        document.getElementById('system-status').textContent = status.health.toUpperCase();
        
        if (metrics) {
            const memoryPercent = metrics.memory.systemUsedPercent.toFixed(1);
            document.getElementById('memory-usage').textContent = `${memoryPercent}%`;
        }
    }

    updateSystemMetrics(metrics) {
        if (metrics) {
            const memoryPercent = metrics.memory.systemUsedPercent.toFixed(1);
            document.getElementById('memory-usage').textContent = `${memoryPercent}%`;

            // Update performance indicators
            this.updatePerformanceMetrics(metrics);

            // Add to chart data
            this.memoryData.push({
                time: new Date(metrics.timestamp),
                value: metrics.memory.systemUsedPercent
            });

            if (this.memoryData.length > this.maxDataPoints) {
                this.memoryData.shift();
            }

            this.updateMemoryChart();
        }
    }

    updatePerformanceMetrics(metrics) {
        // Update memory progress bar
        const memoryPercent = metrics.memory.systemUsedPercent;
        document.getElementById('memory-progress').style.width = `${memoryPercent}%`;
        document.getElementById('memory-percentage').textContent = `${memoryPercent.toFixed(1)}%`;

        // Update CPU load (using load average as approximation)
        if (metrics.cpu && metrics.cpu.loadAverage) {
            const cpuPercent = Math.min((metrics.cpu.loadAverage[0] / metrics.cpu.cpuCount) * 100, 100);
            document.getElementById('cpu-progress').style.width = `${cpuPercent}%`;
            document.getElementById('cpu-percentage').textContent = `${cpuPercent.toFixed(1)}%`;
        }
    }

    updateQueueStats(queue) {
        if (queue) {
            document.getElementById('queue-size').textContent = queue.total.toLocaleString();

            // Update queue progress bar (assuming max 1000 for visualization)
            const queuePercent = Math.min((queue.total / 1000) * 100, 100);
            document.getElementById('queue-progress').style.width = `${queuePercent}%`;
            document.getElementById('queue-percentage').textContent = queue.total.toLocaleString();

            this.queueData.push({
                time: new Date(),
                waiting: queue.waiting,
                active: queue.active,
                failed: queue.failed
            });

            if (this.queueData.length > this.maxDataPoints) {
                this.queueData.shift();
            }

            this.updateQueueChart();

            // Log queue activity
            this.addLogEntry(`Queue: ${queue.waiting} waiting, ${queue.active} active, ${queue.failed} failed`, 'info');
        }
    }

    updatePairs(pairs) {
        this.pairs = pairs || [];
        this.filterPairs();
        this.renderPairs();
        this.updateProblemPairs();
    }

    filterPairs() {
        const filterText = document.getElementById('pair-filter').value.toLowerCase();
        const statusFilter = document.getElementById('status-filter').value;
        
        this.filteredPairs = this.pairs.filter(pair => {
            const matchesText = pair.pair.toLowerCase().includes(filterText);
            const matchesStatus = !statusFilter || pair.status === statusFilter;
            return matchesText && matchesStatus;
        });
    }

    renderPairs() {
        const container = document.getElementById('pairs-container');
        container.innerHTML = '';
        
        this.filteredPairs.forEach(pair => {
            const pairElement = this.createPairElement(pair);
            container.appendChild(pairElement);
        });
    }

    createPairElement(pair) {
        const div = document.createElement('div');
        div.className = `pair-card p-4 bg-gray-50 rounded-lg ${this.getPairStatusClass(pair)}`;
        
        const timeSince = pair.timeSinceLastUpdate ? 
            this.formatTimeSince(pair.timeSinceLastUpdate) : 'Never';
        
        div.innerHTML = `
            <div class="flex justify-between items-center">
                <div class="flex-1">
                    <div class="flex items-center">
                        <span class="status-indicator ${this.getStatusIndicatorClass(pair)}"></span>
                        <h4 class="font-semibold text-gray-900 uppercase">${pair.pair}</h4>
                    </div>
                    <div class="mt-1 text-sm text-gray-600">
                        <span>Records: ${pair.recordCount.toLocaleString()}</span>
                        <span class="mx-2">•</span>
                        <span>Last: ${timeSince}</span>
                        ${pair.errors > 0 ? `<span class="mx-2">•</span><span class="text-red-600">Errors: ${pair.errors}</span>` : ''}
                    </div>
                </div>
                <div class="flex space-x-2">
                    <button onclick="dashboard.startPair('${pair.pair}')" 
                            class="bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs transition-colors">
                        <i class="fas fa-play"></i>
                    </button>
                    <button onclick="dashboard.stopPair('${pair.pair}')" 
                            class="bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs transition-colors">
                        <i class="fas fa-stop"></i>
                    </button>
                </div>
            </div>
        `;
        
        return div;
    }

    getPairStatusClass(pair) {
        switch (pair.status) {
            case 'active': return 'active';
            case 'error': return 'error';
            case 'stopped': return 'warning';
            default: return '';
        }
    }

    getStatusIndicatorClass(pair) {
        switch (pair.status) {
            case 'active': return 'status-healthy';
            case 'error': return 'status-error';
            case 'stopped': return 'status-warning';
            default: return 'status-unknown';
        }
    }

    updateProblemPairs() {
        const problemPairs = this.pairs.filter(pair => 
            pair.status === 'error' || 
            pair.errors > 0 || 
            (pair.timeSinceLastUpdate && pair.timeSinceLastUpdate > 300000) // 5 minutes
        );
        
        const container = document.getElementById('problem-pairs');
        container.innerHTML = '';
        
        if (problemPairs.length === 0) {
            container.innerHTML = '<p class="text-gray-500 text-sm">No problems detected</p>';
            return;
        }
        
        problemPairs.forEach(pair => {
            const div = document.createElement('div');
            div.className = 'p-3 bg-red-50 border border-red-200 rounded-lg';
            div.innerHTML = `
                <div class="flex justify-between items-center">
                    <span class="font-medium text-red-900 uppercase">${pair.pair}</span>
                    <span class="text-xs text-red-600">${pair.status}</span>
                </div>
                <p class="text-xs text-red-700 mt-1">
                    ${pair.errors > 0 ? `${pair.errors} errors` : 'No recent data'}
                </p>
            `;
            container.appendChild(div);
        });
    }

    formatTimeSince(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) return `${hours}h ${minutes % 60}m ago`;
        if (minutes > 0) return `${minutes}m ${seconds % 60}s ago`;
        return `${seconds}s ago`;
    }

    updateRedisStatus(redis) {
        const container = document.getElementById('redis-status');
        container.innerHTML = `
            <div class="flex items-center">
                <span class="status-indicator ${redis.connected ? 'status-healthy' : 'status-error'}"></span>
                <span class="font-medium">${redis.connected ? 'Connected' : 'Disconnected'}</span>
            </div>
            ${redis.error ? `<p class="text-red-600 text-sm mt-1">${redis.error}</p>` : ''}
        `;
    }

    updateStorageDetails(storage) {
        if (!storage) return;
        
        document.getElementById('storage-used').textContent = `${storage.totalSizeMB} MB`;
        
        const container = document.getElementById('storage-details');
        container.innerHTML = `
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-gray-600">Total Size:</span>
                    <span class="font-medium">${storage.totalSizeMB} MB</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">File Count:</span>
                    <span class="font-medium">${storage.fileCount}</span>
                </div>
                <div class="mt-3">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Recent Files:</h4>
                    <div class="space-y-1 max-h-32 overflow-y-auto scrollbar-thin">
                        ${storage.files.slice(0, 5).map(file => `
                            <div class="text-xs text-gray-600">
                                <div class="flex justify-between">
                                    <span>${file.name}</span>
                                    <span>${(file.size / (1024 * 1024)).toFixed(1)} MB</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    updateLastUpdate() {
        document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
    }

    hideLoading() {
        setTimeout(() => {
            document.getElementById('loading-overlay').style.display = 'none';
        }, 1000);
    }

    initCharts() {
        // Memory Chart
        const memoryCtx = document.getElementById('memoryChart').getContext('2d');
        this.memoryChart = new Chart(memoryCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Memory Usage (%)',
                    data: [],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    },
                    x: {
                        type: 'time',
                        time: {
                            displayFormats: {
                                minute: 'HH:mm'
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // Queue Chart
        const queueCtx = document.getElementById('queueChart').getContext('2d');
        this.queueChart = new Chart(queueCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'Waiting',
                        data: [],
                        borderColor: 'rgb(245, 158, 11)',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: 'Active',
                        data: [],
                        borderColor: 'rgb(16, 185, 129)',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: 'Failed',
                        data: [],
                        borderColor: 'rgb(239, 68, 68)',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    },
                    x: {
                        type: 'time',
                        time: {
                            displayFormats: {
                                minute: 'HH:mm'
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    }

    updateMemoryChart() {
        if (!this.memoryChart) return;

        this.memoryChart.data.labels = this.memoryData.map(d => d.time);
        this.memoryChart.data.datasets[0].data = this.memoryData.map(d => d.value);
        this.memoryChart.update('none');
    }

    updateQueueChart() {
        if (!this.queueChart) return;

        this.queueChart.data.labels = this.queueData.map(d => d.time);
        this.queueChart.data.datasets[0].data = this.queueData.map(d => d.waiting);
        this.queueChart.data.datasets[1].data = this.queueData.map(d => d.active);
        this.queueChart.data.datasets[2].data = this.queueData.map(d => d.failed);
        this.queueChart.update('none');
    }

    setupEventListeners() {
        // Refresh button
        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.socket.emit('request-update');
        });

        // Filter inputs
        document.getElementById('pair-filter').addEventListener('input', () => {
            this.filterPairs();
            this.renderPairs();
        });

        document.getElementById('status-filter').addEventListener('change', () => {
            this.filterPairs();
            this.renderPairs();
        });

        // Start/Stop all buttons
        document.getElementById('start-all-btn').addEventListener('click', () => {
            this.startAllPairs();
        });

        document.getElementById('stop-all-btn').addEventListener('click', () => {
            this.stopAllPairs();
        });

        // Activity log controls
        document.getElementById('clear-log-btn').addEventListener('click', () => {
            this.clearActivityLog();
        });

        document.getElementById('pause-log-btn').addEventListener('click', () => {
            this.toggleLogPause();
        });
    }

    addLogEntry(message, type = 'info') {
        if (this.logPaused) return;

        const timestamp = new Date().toLocaleTimeString();
        const entry = {
            timestamp,
            message,
            type,
            id: Date.now()
        };

        this.activityLog.unshift(entry);

        // Keep only the last N entries
        if (this.activityLog.length > this.maxLogEntries) {
            this.activityLog = this.activityLog.slice(0, this.maxLogEntries);
        }

        this.updateActivityLogDisplay();
    }

    updateActivityLogDisplay() {
        const container = document.getElementById('activity-log');

        if (this.activityLog.length === 0) {
            container.innerHTML = '<div class="text-gray-500">Activity log will appear here...</div>';
            return;
        }

        const logHtml = this.activityLog.map(entry => {
            const colorClass = {
                'info': 'text-blue-400',
                'success': 'text-green-400',
                'warning': 'text-yellow-400',
                'error': 'text-red-400'
            }[entry.type] || 'text-green-400';

            return `
                <div class="flex">
                    <span class="text-gray-500 mr-2">[${entry.timestamp}]</span>
                    <span class="${colorClass}">${entry.message}</span>
                </div>
            `;
        }).join('');

        container.innerHTML = logHtml;

        // Auto-scroll to top for new entries
        container.scrollTop = 0;
    }

    clearActivityLog() {
        this.activityLog = [];
        this.updateActivityLogDisplay();
        this.addLogEntry('Activity log cleared', 'info');
    }

    toggleLogPause() {
        this.logPaused = !this.logPaused;
        const btn = document.getElementById('pause-log-btn');

        if (this.logPaused) {
            btn.innerHTML = '<i class="fas fa-play mr-1"></i>Resume';
            btn.className = 'bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm transition-colors';
            this.addLogEntry('Activity log paused', 'warning');
        } else {
            btn.innerHTML = '<i class="fas fa-pause mr-1"></i>Pause';
            btn.className = 'bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors';
            this.addLogEntry('Activity log resumed', 'info');
        }
    }

    async startPair(pair) {
        try {
            this.addLogEntry(`Starting pair ${pair.toUpperCase()}...`, 'info');

            const response = await fetch(`/api/pairs/${pair}/start`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                this.showNotification(`Started ${pair}`, 'success');
                this.addLogEntry(`Successfully started ${pair.toUpperCase()}`, 'success');
                this.socket.emit('request-update');
            } else {
                this.showNotification(`Failed to start ${pair}`, 'error');
                this.addLogEntry(`Failed to start ${pair.toUpperCase()}`, 'error');
            }
        } catch (error) {
            this.showNotification(`Error starting ${pair}: ${error.message}`, 'error');
            this.addLogEntry(`Error starting ${pair.toUpperCase()}: ${error.message}`, 'error');
        }
    }

    async stopPair(pair) {
        try {
            this.addLogEntry(`Stopping pair ${pair.toUpperCase()}...`, 'info');

            const response = await fetch(`/api/pairs/${pair}/stop`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                this.showNotification(`Stopped ${pair}`, 'success');
                this.addLogEntry(`Successfully stopped ${pair.toUpperCase()}`, 'success');
                this.socket.emit('request-update');
            } else {
                this.showNotification(`Failed to stop ${pair}`, 'error');
                this.addLogEntry(`Failed to stop ${pair.toUpperCase()}`, 'error');
            }
        } catch (error) {
            this.showNotification(`Error stopping ${pair}: ${error.message}`, 'error');
            this.addLogEntry(`Error stopping ${pair.toUpperCase()}: ${error.message}`, 'error');
        }
    }

    async startAllPairs() {
        for (const pair of this.pairs) {
            await this.startPair(pair.pair);
            await new Promise(resolve => setTimeout(resolve, 100)); // Small delay
        }
    }

    async stopAllPairs() {
        for (const pair of this.pairs) {
            await this.stopPair(pair.pair);
            await new Promise(resolve => setTimeout(resolve, 100)); // Small delay
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new DashboardManager();
});
