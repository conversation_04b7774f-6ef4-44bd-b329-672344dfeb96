<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binance Tick Collector - Monitoring Dashboard</title>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-healthy { background-color: #10b981; }
        .status-warning { background-color: #f59e0b; }
        .status-error { background-color: #ef4444; }
        .status-unknown { background-color: #6b7280; }
        
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .pair-card {
            border-left: 4px solid #e5e7eb;
            transition: border-color 0.3s ease;
        }
        
        .pair-card.active {
            border-left-color: #10b981;
        }
        
        .pair-card.error {
            border-left-color: #ef4444;
        }
        
        .pair-card.warning {
            border-left-color: #f59e0b;
        }
        
        .animate-pulse-slow {
            animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        .scrollbar-thin {
            scrollbar-width: thin;
        }
        
        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <i class="fas fa-chart-line text-blue-600 text-2xl mr-3"></i>
                    <h1 class="text-2xl font-bold text-gray-900">Binance Tick Collector</h1>
                    <span id="connection-status" class="ml-4 px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                        <span class="status-indicator status-unknown"></span>
                        Connecting...
                    </span>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="refresh-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                    <div class="text-sm text-gray-500">
                        Last Update: <span id="last-update">--</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- System Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- System Status -->
            <div class="card p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">System Status</p>
                        <p id="system-status" class="text-2xl font-bold text-gray-900">--</p>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <i class="fas fa-server text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Memory Usage -->
            <div class="card p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Memory Usage</p>
                        <p id="memory-usage" class="text-2xl font-bold text-gray-900">--</p>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-memory text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Queue Size -->
            <div class="card p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Queue Size</p>
                        <p id="queue-size" class="text-2xl font-bold text-gray-900">--</p>
                    </div>
                    <div class="p-3 bg-yellow-100 rounded-full">
                        <i class="fas fa-list text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Storage Used -->
            <div class="card p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Storage Used</p>
                        <p id="storage-used" class="text-2xl font-bold text-gray-900">--</p>
                    </div>
                    <div class="p-3 bg-purple-100 rounded-full">
                        <i class="fas fa-database text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Memory Chart -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Memory Usage Over Time</h3>
                <canvas id="memoryChart" width="400" height="200"></canvas>
            </div>

            <!-- Queue Chart -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Queue Statistics</h3>
                <canvas id="queueChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Pairs Management -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Active Pairs -->
            <div class="lg:col-span-2">
                <div class="card p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Trading Pairs Status</h3>
                        <div class="flex space-x-2">
                            <button id="start-all-btn" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors">
                                <i class="fas fa-play mr-1"></i>Start All
                            </button>
                            <button id="stop-all-btn" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors">
                                <i class="fas fa-stop mr-1"></i>Stop All
                            </button>
                        </div>
                    </div>
                    
                    <!-- Filter Controls -->
                    <div class="mb-4 flex flex-wrap gap-2">
                        <input type="text" id="pair-filter" placeholder="Filter pairs..." 
                               class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <select id="status-filter" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="stopped">Stopped</option>
                            <option value="error">Error</option>
                        </select>
                    </div>
                    
                    <div id="pairs-container" class="space-y-3 max-h-96 overflow-y-auto scrollbar-thin">
                        <!-- Pairs will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Problem Pairs -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                    Problem Pairs
                </h3>
                <div id="problem-pairs" class="space-y-2 max-h-96 overflow-y-auto scrollbar-thin">
                    <!-- Problem pairs will be populated here -->
                </div>
            </div>
        </div>

        <!-- System Details -->
        <div class="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Redis Status -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-database text-red-500 mr-2"></i>
                    Redis Status
                </h3>
                <div id="redis-status" class="space-y-2">
                    <!-- Redis status will be populated here -->
                </div>
            </div>

            <!-- Storage Details -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-hdd text-purple-500 mr-2"></i>
                    Storage Details
                </h3>
                <div id="storage-details" class="space-y-2">
                    <!-- Storage details will be populated here -->
                </div>
            </div>

            <!-- System Performance -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-tachometer-alt text-blue-500 mr-2"></i>
                    Performance
                </h3>
                <div id="performance-metrics" class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">CPU Load</span>
                        <div class="flex items-center">
                            <div class="w-20 h-2 bg-gray-200 rounded-full mr-2">
                                <div id="cpu-progress" class="h-2 bg-blue-500 rounded-full" style="width: 0%"></div>
                            </div>
                            <span id="cpu-percentage" class="text-sm font-medium">0%</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Memory</span>
                        <div class="flex items-center">
                            <div class="w-20 h-2 bg-gray-200 rounded-full mr-2">
                                <div id="memory-progress" class="h-2 bg-green-500 rounded-full" style="width: 0%"></div>
                            </div>
                            <span id="memory-percentage" class="text-sm font-medium">0%</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Queue Load</span>
                        <div class="flex items-center">
                            <div class="w-20 h-2 bg-gray-200 rounded-full mr-2">
                                <div id="queue-progress" class="h-2 bg-yellow-500 rounded-full" style="width: 0%"></div>
                            </div>
                            <span id="queue-percentage" class="text-sm font-medium">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity Log -->
        <div class="mt-8">
            <div class="card p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-list-alt text-gray-500 mr-2"></i>
                        Activity Log
                    </h3>
                    <div class="flex space-x-2">
                        <button id="clear-log-btn" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm transition-colors">
                            <i class="fas fa-trash mr-1"></i>Clear
                        </button>
                        <button id="pause-log-btn" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors">
                            <i class="fas fa-pause mr-1"></i>Pause
                        </button>
                    </div>
                </div>
                <div id="activity-log" class="bg-gray-900 text-green-400 p-4 rounded-lg h-64 overflow-y-auto font-mono text-sm custom-scrollbar">
                    <div class="text-gray-500">Activity log will appear here...</div>
                </div>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-8 text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Loading dashboard...</p>
        </div>
    </div>

    <script src="dashboard.js"></script>
</body>
</html>
